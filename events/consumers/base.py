"""
Base consumer classes for event processing
"""
import json
import logging
import signal
import sys
import threading
import time
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from confluent_kafka import Consumer, KafkaError, KafkaException

from ..kafka_config import KafkaConfig
from ..schemas import BaseEvent, EventFactory

logger = logging.getLogger(__name__)


class BaseEventConsumer(ABC):
    """Base class for all event consumers"""
    
    def __init__(self, group_id: str, topics: List[str], max_poll_records: int = 100):
        self.group_id = group_id
        self.topics = topics
        self.max_poll_records = max_poll_records
        self.consumer = None
        self.running = False
        self.shutdown_event = threading.Event()
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown()
    
    def _initialize_consumer(self):
        """Initialize Kafka consumer"""
        try:
            config = KafkaConfig.get_consumer_config(self.group_id)
            self.consumer = Consumer(config)
            self.consumer.subscribe(self.topics)
            logger.info(f"Consumer {self.group_id} initialized for topics: {self.topics}")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize consumer {self.group_id}: {e}")
            return False
    
    def start(self):
        """Start the consumer"""
        if not self._initialize_consumer():
            return False
        
        self.running = True
        logger.info(f"Starting consumer {self.group_id}...")
        
        try:
            while self.running and not self.shutdown_event.is_set():
                self._poll_and_process()
        except KeyboardInterrupt:
            logger.info("Consumer interrupted by user")
        except Exception as e:
            logger.error(f"Consumer {self.group_id} error: {e}")
        finally:
            self._cleanup()
        
        return True
    
    def _poll_and_process(self):
        """Poll for messages and process them"""
        try:
            # Poll for messages
            msg = self.consumer.poll(timeout=1.0)
            
            if msg is None:
                return
            
            if msg.error():
                if msg.error().code() == KafkaError._PARTITION_EOF:
                    logger.debug(f"Reached end of partition {msg.partition()}")
                else:
                    logger.error(f"Consumer error: {msg.error()}")
                return
            
            # Process the message
            try:
                self._process_message(msg)
                # Commit the offset after successful processing
                self.consumer.commit(message=msg)
            except Exception as e:
                logger.error(f"Error processing message: {e}")
                # Decide whether to retry or send to DLQ
                self._handle_processing_error(msg, e)
                
        except Exception as e:
            logger.error(f"Error in poll loop: {e}")
            time.sleep(1)  # Brief pause to avoid tight error loop
    
    def _process_message(self, msg):
        """Process a single message"""
        try:
            # Extract message data
            value = msg.value().decode('utf-8')
            event_data = json.loads(value)
            
            # Extract headers
            headers = {}
            if msg.headers():
                headers = {k: v.decode('utf-8') if isinstance(v, bytes) else v 
                          for k, v in msg.headers()}
            
            # Log processing start
            logger.debug(f"Processing event {headers.get('event_type', 'unknown')} "
                        f"for video {headers.get('video_id', 'unknown')}")
            
            # Call the specific handler
            success = self.handle_event(event_data, headers)
            
            if not success:
                raise Exception("Event handler returned False")
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode message JSON: {e}")
            raise
        except Exception as e:
            logger.error(f"Error in message processing: {e}")
            raise
    
    @abstractmethod
    def handle_event(self, event_data: Dict[str, Any], headers: Dict[str, str]) -> bool:
        """
        Handle a specific event - must be implemented by subclasses
        
        Args:
            event_data: The event data dictionary
            headers: Message headers
            
        Returns:
            bool: True if processing was successful, False otherwise
        """
        pass
    
    def _handle_processing_error(self, msg, error):
        """Handle processing errors"""
        try:
            # Extract event info
            headers = {}
            if msg.headers():
                headers = {k: v.decode('utf-8') if isinstance(v, bytes) else v 
                          for k, v in msg.headers()}
            
            event_type = headers.get('event_type', 'unknown')
            video_id = headers.get('video_id', 'unknown')
            
            logger.error(f"Failed to process {event_type} for video {video_id}: {error}")
            
            # Send to dead letter queue
            self._send_to_dlq(msg, str(error))
            
            # Commit the offset to avoid reprocessing
            self.consumer.commit(message=msg)
            
        except Exception as e:
            logger.error(f"Error handling processing error: {e}")
    
    def _send_to_dlq(self, msg, error_message: str):
        """Send failed message to dead letter queue"""
        try:
            from ..producer import EventProducer
            
            # Create DLQ event data
            dlq_data = {
                'original_topic': msg.topic(),
                'original_partition': msg.partition(),
                'original_offset': msg.offset(),
                'original_message': msg.value().decode('utf-8'),
                'error_message': error_message,
                'consumer_group': self.group_id,
                'failed_at': datetime.utcnow().isoformat(),
                'headers': dict(msg.headers()) if msg.headers() else {}
            }
            
            # Publish to DLQ topic
            producer = EventProducer()
            if producer.producer:
                producer.producer.produce(
                    topic=KafkaConfig.TOPICS['DLQ_EVENTS'],
                    value=json.dumps(dlq_data),
                    key=str(msg.key()) if msg.key() else None
                )
                producer.producer.flush()
                
        except Exception as e:
            logger.error(f"Failed to send message to DLQ: {e}")
    
    def shutdown(self):
        """Gracefully shutdown the consumer"""
        logger.info(f"Shutting down consumer {self.group_id}...")
        self.running = False
        self.shutdown_event.set()
    
    def _cleanup(self):
        """Cleanup resources"""
        if self.consumer:
            try:
                self.consumer.close()
                logger.info(f"Consumer {self.group_id} closed successfully")
            except Exception as e:
                logger.error(f"Error closing consumer {self.group_id}: {e}")


class BatchEventConsumer(BaseEventConsumer):
    """Consumer that processes events in batches"""
    
    def __init__(self, group_id: str, topics: List[str], batch_size: int = 10, batch_timeout: float = 5.0):
        super().__init__(group_id, topics)
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.message_batch = []
        self.batch_start_time = None
    
    def _poll_and_process(self):
        """Poll for messages and process them in batches"""
        try:
            msg = self.consumer.poll(timeout=1.0)
            
            if msg is None:
                # Check if we should process current batch due to timeout
                if self.message_batch and self._should_process_batch():
                    self._process_batch()
                return
            
            if msg.error():
                if msg.error().code() == KafkaError._PARTITION_EOF:
                    logger.debug(f"Reached end of partition {msg.partition()}")
                else:
                    logger.error(f"Consumer error: {msg.error()}")
                return
            
            # Add message to batch
            self.message_batch.append(msg)
            
            if self.batch_start_time is None:
                self.batch_start_time = time.time()
            
            # Process batch if size or timeout reached
            if len(self.message_batch) >= self.batch_size or self._should_process_batch():
                self._process_batch()
                
        except Exception as e:
            logger.error(f"Error in batch poll loop: {e}")
            time.sleep(1)
    
    def _should_process_batch(self) -> bool:
        """Check if batch should be processed based on timeout"""
        if not self.message_batch or self.batch_start_time is None:
            return False
        
        return (time.time() - self.batch_start_time) >= self.batch_timeout
    
    def _process_batch(self):
        """Process the current batch of messages"""
        if not self.message_batch:
            return
        
        try:
            # Prepare batch data
            batch_events = []
            for msg in self.message_batch:
                try:
                    value = msg.value().decode('utf-8')
                    event_data = json.loads(value)
                    
                    headers = {}
                    if msg.headers():
                        headers = {k: v.decode('utf-8') if isinstance(v, bytes) else v 
                                  for k, v in msg.headers()}
                    
                    batch_events.append({
                        'event_data': event_data,
                        'headers': headers,
                        'message': msg
                    })
                except Exception as e:
                    logger.error(f"Error preparing batch event: {e}")
                    self._handle_processing_error(msg, e)
            
            if batch_events:
                # Process the batch
                success = self.handle_batch(batch_events)
                
                if success:
                    # Commit all messages in batch
                    for item in batch_events:
                        self.consumer.commit(message=item['message'])
                else:
                    # Handle batch failure
                    logger.error(f"Batch processing failed for {len(batch_events)} events")
                    for item in batch_events:
                        self._handle_processing_error(item['message'], Exception("Batch processing failed"))
            
        except Exception as e:
            logger.error(f"Error processing batch: {e}")
            # Handle individual message errors
            for msg in self.message_batch:
                self._handle_processing_error(msg, e)
        finally:
            # Clear the batch
            self.message_batch.clear()
            self.batch_start_time = None
    
    @abstractmethod
    def handle_batch(self, batch_events: List[Dict[str, Any]]) -> bool:
        """
        Handle a batch of events - must be implemented by subclasses
        
        Args:
            batch_events: List of event dictionaries with 'event_data', 'headers', and 'message'
            
        Returns:
            bool: True if batch processing was successful, False otherwise
        """
        pass
    
    def handle_event(self, event_data: Dict[str, Any], headers: Dict[str, str]) -> bool:
        """Single event handling not used in batch consumer"""
        return True
