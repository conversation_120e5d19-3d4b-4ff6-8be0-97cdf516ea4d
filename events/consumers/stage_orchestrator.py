"""
Stage orchestrator consumer - handles stage transitions and workflow logic
SIMPLIFIED: All logic delegated to VideoCreationService for clean separation
"""
import logging
from typing import Dict, Any, List

from events.schemas import EventType

from .base import BaseEventConsumer
from ..kafka_config import KafkaConfig
from ..producer import event_publisher

logger = logging.getLogger(__name__)


class StageOrchestratorConsumer(BaseEventConsumer):
    """
    Handles stage completion events and orchestrates workflow transitions
    """
    
    def __init__(self):
        topics = [KafkaConfig.TOPICS['STAGE_EVENTS']]
        super().__init__(
            group_id='stage-orchestrator',
            topics=topics
        )
    
    def handle_event(self, event_data: Dict[str, Any], headers: Dict[str, str]) -> bool:
        """Handle stage-related events"""
        try:
            event_type = headers.get('event_type')
            
            if event_type == EventType.STAGE_COMPLETED.value:
                return self._handle_stage_completed(event_data)
            elif event_type == EventType.STAGE_FAILED.value:
                return self._handle_stage_failed(event_data)
            elif event_type == EventType.STAGE_INITIATED.value:
                return self._handle_stage_initiated(event_data)
            else:
                logger.debug(f"Ignoring event type: {event_type}")
                return True
                
        except Exception as e:
            logger.error(f"Error handling stage event: {e}")
            return False
    
    def _handle_stage_completed(self, event_data: Dict[str, Any]) -> bool:
        """Handle stage completion - delegate to video creation service"""
        try:
            video_id = event_data['video_id']
            stage = event_data['stage']
            correlation_id = event_data['correlation_id']
            stage_data = event_data.get('stage_data', {})
            
            logger.info(f"Delegating stage completion to video service: {stage} for video {video_id}")
            
            # Delegate to video creation service
            from videos.services.video_creation_service import video_creation_service
            result = video_creation_service.handle_stage_completion(
                video_id=video_id,
                stage=stage,
                correlation_id=correlation_id,
                stage_data=stage_data
            )
            
            if result['success']:
                logger.info(f"Video service successfully handled stage completion: {stage} for video {video_id}")
                return True
            else:
                logger.error(f"Video service failed to handle stage completion: {result.get('error')}")
                return False
            
        except Exception as e:
            logger.error(f"Error delegating stage completion: {e}")
            return False
    
    def _handle_stage_failed(self, event_data: Dict[str, Any]) -> bool:
        """Handle stage failure - delegate to video creation service"""
        try:
            video_id = event_data['video_id']
            stage = event_data['stage']
            correlation_id = event_data['correlation_id']
            error_message = event_data.get('error_message', 'Unknown error')
            error_code = event_data.get('error_code')
            
            logger.warning(f"Delegating stage failure to video service: {stage} for video {video_id}")
            
            # Delegate to video creation service
            from videos.services.video_creation_service import video_creation_service
            result = video_creation_service.handle_stage_failure(
                video_id=video_id,
                stage=stage,
                correlation_id=correlation_id,
                error_message=error_message,
                error_code=error_code
            )
            
            if result['success']:
                logger.info(f"Video service successfully handled stage failure: {stage} for video {video_id}")
                return True
            else:
                logger.error(f"Video service failed to handle stage failure: {result.get('error')}")
                return False
            
        except Exception as e:
            logger.error(f"Error delegating stage failure: {e}")
            return False
    
    def _handle_stage_initiated(self, event_data: Dict[str, Any]) -> bool:
        """Handle stage initiation - delegate to video creation service"""
        try:
            video_id = event_data['video_id']
            stage = event_data['stage']
            correlation_id = event_data['correlation_id']
            
            logger.info(f"Delegating stage initiation to video service: {stage} for video {video_id}")
            
            # Log the event
            from ..models import VideoEventLog
            VideoEventLog.objects.create(
                video_id=video_id,
                event_type=EventType.STAGE_INITIATED.value,
                stage=stage,
                correlation_id=correlation_id,
                event_data=event_data,
                status='processed'
            )
            
            # Get video instance and delegate to video creation service
            from videos.models import Video
            from videos.services.video_creation_service import video_creation_service
            
            try:
                video = Video.objects.get(id=video_id)
            except Video.DoesNotExist:
                logger.error(f"Video {video_id} not found")
                return False
            
            # Delegate to video creation service
            success = video_creation_service.handle_stage_initiation(
                video=video,
                stage=stage,
                correlation_id=correlation_id
            )
            
            if not success:
                logger.error(f"Video service failed to initiate stage processing for {stage} on video {video_id}")
                return False
            
            logger.info(f"Video service successfully initiated stage processing: {stage} for video {video_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error delegating stage initiation: {e}")
            return False
