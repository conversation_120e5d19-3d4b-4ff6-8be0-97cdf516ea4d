"""
Event producer for publishing events to Kafka
"""
import json
import logging
from typing import Any, Dict, Optional
from confluent_kafka import Producer, KafkaError
from django.conf import settings

from .schemas import BaseEvent
from .kafka_config import KafkaConfig, get_topic_for_event_type, get_partition_key

logger = logging.getLogger(__name__)


class EventProducer:
    """Handles publishing events to Kafka topics"""
    
    def __init__(self):
        self.producer = None
        self._initialize_producer()
    
    def _initialize_producer(self):
        """Initialize Kafka producer"""
        try:
            config = KafkaConfig.get_producer_config()
            self.producer = Producer(config)
            logger.info("Kafka producer initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Kafka producer: {e}")
            self.producer = None
    
    def publish_event(self, event: BaseEvent, retry_count: int = 0) -> bool:
        """
        Publish an event to the appropriate Kafka topic
        
        Args:
            event: The event to publish
            retry_count: Number of retries (for internal use)
            
        Returns:
            bool: True if published successfully, False otherwise
        """
        if not self.producer:
            logger.error("Kafka producer not initialized")
            return self._fallback_to_database(event)
        
        try:
            # Determine topic and partition key
            topic = get_topic_for_event_type(event.event_type)
            partition_key = get_partition_key(event.video_id)
            
            # Serialize event
            event_data = event.to_dict()
            message_value = json.dumps(event_data, default=str)
            
            # Publish to Kafka
            self.producer.produce(
                topic=topic,
                key=partition_key,
                value=message_value,
                callback=self._delivery_callback,
                headers={
                    'event_type': event.event_type,
                    'event_id': event.event_id,
                    'correlation_id': event.correlation_id,
                    'video_id': str(event.video_id)
                }
            )
            
            # Trigger delivery (non-blocking)
            self.producer.poll(0)
            
            logger.debug(f"Published event {event.event_type} for video {event.video_id} to topic {topic}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to publish event {event.event_type} for video {event.video_id}: {e}")
            
            # Retry logic
            if retry_count < 2:
                logger.info(f"Retrying event publication (attempt {retry_count + 1})")
                return self.publish_event(event, retry_count + 1)
            
            # Fallback to database storage
            return self._fallback_to_database(event)
    
    def publish_event_sync(self, event: BaseEvent, timeout: float = 10.0) -> bool:
        """
        Publish an event synchronously (waits for acknowledgment)
        
        Args:
            event: The event to publish
            timeout: Timeout in seconds
            
        Returns:
            bool: True if published successfully, False otherwise
        """
        if not self.producer:
            logger.error("Kafka producer not initialized")
            return self._fallback_to_database(event)
        
        try:
            # Determine topic and partition key
            topic = get_topic_for_event_type(event.event_type)
            partition_key = get_partition_key(event.video_id)
            
            # Serialize event
            event_data = event.to_dict()
            message_value = json.dumps(event_data, default=str)
            
            # Publish to Kafka
            self.producer.produce(
                topic=topic,
                key=partition_key,
                value=message_value,
                headers={
                    'event_type': event.event_type,
                    'event_id': event.event_id,
                    'correlation_id': event.correlation_id,
                    'video_id': str(event.video_id)
                }
            )
            
            # Wait for delivery confirmation
            self.producer.flush(timeout)
            
            logger.info(f"Published event {event.event_type} for video {event.video_id} to topic {topic} (sync)")
            return True
            
        except Exception as e:
            logger.error(f"Failed to publish event {event.event_type} for video {event.video_id} (sync): {e}")
            return self._fallback_to_database(event)
    
    def _delivery_callback(self, err, msg):
        """Callback for message delivery confirmation"""
        if err:
            logger.error(f"Message delivery failed: {err}")
        else:
            logger.debug(f"Message delivered to {msg.topic()} [{msg.partition()}] at offset {msg.offset()}")
    
    def _fallback_to_database(self, event: BaseEvent) -> bool:
        """
        Fallback mechanism to store events in database when Kafka is unavailable
        
        Args:
            event: The event to store
            
        Returns:
            bool: True if stored successfully, False otherwise
        """
        try:
            from .models import EventStore
            
            EventStore.objects.create(
                event_id=event.event_id,
                event_type=event.event_type,
                video_id=event.video_id,
                correlation_id=event.correlation_id,
                event_data=event.to_dict(),
                status='pending',
                created_at=event.timestamp
            )
            
            logger.warning(f"Event {event.event_type} for video {event.video_id} stored in database fallback")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store event in database fallback: {e}")
            return False
    
    def flush(self, timeout: float = 10.0):
        """Flush all pending messages"""
        if self.producer:
            self.producer.flush(timeout)
    
    def close(self):
        """Close the producer"""
        if self.producer:
            self.producer.flush()
            self.producer = None


class EventPublisher:
    """High-level interface for publishing events"""
    
    def __init__(self):
        self.producer = EventProducer()
    
    def publish_stage_initiated(self, video_id: int, stage: str, correlation_id: str, **kwargs) -> bool:
        """Publish StageInitiated event"""
        from .schemas import EventFactory
        
        event = EventFactory.create_stage_initiated(
            video_id=video_id,
            stage=stage,
            correlation_id=correlation_id,
            **kwargs
        )
        
        return self.producer.publish_event(event)
    
    def publish_stage_completed(self, video_id: int, stage: str, correlation_id: str, **kwargs) -> bool:
        """Publish StageCompleted event"""
        from .schemas import EventFactory
        
        event = EventFactory.create_stage_completed(
            video_id=video_id,
            stage=stage,
            correlation_id=correlation_id,
            **kwargs
        )
        
        return self.producer.publish_event(event)
    
    def publish_stage_failed(self, video_id: int, stage: str, correlation_id: str, error_message: str, **kwargs) -> bool:
        """Publish StageFailed event"""
        from .schemas import EventFactory
        
        event = EventFactory.create_stage_failed(
            video_id=video_id,
            stage=stage,
            correlation_id=correlation_id,
            error_message=error_message,
            **kwargs
        )
        
        return self.producer.publish_event(event)

    def close(self):
        """Close the publisher"""
        self.producer.close()


# Global publisher instance
event_publisher = EventPublisher()
