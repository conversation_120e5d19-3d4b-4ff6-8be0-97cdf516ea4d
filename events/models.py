"""
Database models for event storage and fallback
"""
from django.db import models
import uuid


class EventStore(models.Model):
    """
    Database storage for events when Kaf<PERSON> is unavailable
    """
    event_id = models.UUIDField(unique=True, db_index=True)
    event_type = models.CharField(max_length=100, db_index=True)
    video_id = models.IntegerField(db_index=True)
    correlation_id = models.CharField(max_length=100, db_index=True)
    event_data = models.JSONField()
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('published', 'Published'),
            ('failed', 'Failed'),
        ],
        default='pending',
        db_index=True
    )
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    published_at = models.DateTimeField(null=True, blank=True)
    retry_count = models.IntegerField(default=0)
    error_message = models.TextField(null=True, blank=True)

    class Meta:
        db_table = 'event_store'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['video_id', 'event_type']),
            models.Index(fields=['correlation_id', 'created_at']),
        ]

    def __str__(self):
        return f"{self.event_type} - Video {self.video_id} - {self.status}"


class VideoEventLog(models.Model):
    """
    Audit log for video events and state changes
    """
    video_id = models.IntegerField(db_index=True)
    event_type = models.CharField(max_length=100, db_index=True)
    stage = models.CharField(max_length=50, null=True, blank=True, db_index=True)
    correlation_id = models.CharField(max_length=100, db_index=True)
    event_data = models.JSONField(default=dict)
    status = models.CharField(max_length=20, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    processing_duration_ms = models.IntegerField(null=True, blank=True)

    class Meta:
        db_table = 'video_event_log'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['video_id', 'created_at']),
            models.Index(fields=['correlation_id', 'created_at']),
            models.Index(fields=['stage', 'status']),
        ]

    def __str__(self):
        return f"{self.event_type} - Video {self.video_id} - {self.stage or 'N/A'}"


class WorkflowState(models.Model):
    """
    Current state tracking for video workflows
    """
    video_id = models.OneToOneField('videos.Video', on_delete=models.CASCADE, primary_key=True, related_name='workflow_state')
    correlation_id = models.CharField(max_length=100, db_index=True)
    current_stage = models.CharField(max_length=50, db_index=True)
    stages_completed = models.JSONField(default=list)  # List of completed stages
    stages_failed = models.JSONField(default=list)  # List of failed stages
    retry_counts = models.JSONField(default=dict)  # Stage -> retry count mapping
    is_paused = models.BooleanField(default=False, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_activity_at = models.DateTimeField(auto_now=True, db_index=True)

    class Meta:
        db_table = 'workflow_state'
        indexes = [
            models.Index(fields=['current_stage', 'is_paused']),
            models.Index(fields=['last_activity_at']),
        ]

    def __str__(self):
        return f"Video {self.video_id} - {self.current_stage}"

    def mark_stage_completed(self, stage: str):
        """Mark a stage as completed"""
        if stage not in self.stages_completed:
            self.stages_completed.append(stage)
        # Remove from failed if it was there
        if stage in self.stages_failed:
            self.stages_failed.remove(stage)
        self.save(update_fields=['stages_completed', 'stages_failed', 'updated_at'])

    def mark_stage_failed(self, stage: str):
        """Mark a stage as failed"""
        if stage not in self.stages_failed:
            self.stages_failed.append(stage)
        # Increment retry count (start from 1)
        self.retry_counts[stage] = self.retry_counts.get(stage, 0) + 1
        self.save(update_fields=['stages_failed', 'retry_counts', 'updated_at'])

    def is_stage_completed(self, stage: str) -> bool:
        """Check if a stage is completed"""
        return stage in self.stages_completed

    def is_stage_failed(self, stage: str) -> bool:
        """Check if a stage has failed"""
        return stage in self.stages_failed

    def get_retry_count(self, stage: str) -> int:
        """Get retry count for a stage (starts from 1)"""
        return self.retry_counts.get(stage, 0)


class RetryPolicy(models.Model):
    """
    Retry policies for different event types and stages
    """
    event_type = models.CharField(max_length=100, db_index=True)
    stage = models.CharField(max_length=50, null=True, blank=True, db_index=True)
    max_retries = models.IntegerField(default=3)
    initial_delay_seconds = models.IntegerField(default=60)
    backoff_multiplier = models.FloatField(default=2.0)
    max_delay_seconds = models.IntegerField(default=3600)  # 1 hour
    is_active = models.BooleanField(default=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'retry_policy'
        unique_together = ['event_type', 'stage']
        indexes = [
            models.Index(fields=['event_type', 'is_active']),
        ]

    def __str__(self):
        stage_part = f" - {self.stage}" if self.stage else ""
        return f"{self.event_type}{stage_part} (max {self.max_retries} retries)"

    def calculate_delay(self, retry_count: int) -> int:
        """Calculate delay for a given retry attempt"""
        delay = self.initial_delay_seconds * (self.backoff_multiplier ** retry_count)
        return min(int(delay), self.max_delay_seconds)
