"""
Django management command to initialize Kafka topics
"""
import sys
from django.core.management.base import BaseCommand
from events.kafka_config import KafkaTopicManager


class Command(BaseCommand):
    help = 'Initialize Kafka topics for video creation events'

    def add_arguments(self, parser):
        parser.add_argument(
            '--delete-first',
            action='store_true',
            help='Delete existing topics before creating new ones',
        )
        parser.add_argument(
            '--ignore-errors',
            action='store_true',
            help='Continue execution even if some topics fail to create',
        )

    def handle(self, *args, **options):
        topic_manager = KafkaTopicManager()
        
        if options['delete_first']:
            self.stdout.write('Deleting existing topics...')
            from events.kafka_config import KafkaConfig
            topics_to_delete = list(KafkaConfig.TOPICS.values())
            if topic_manager.delete_topics(topics_to_delete):
                self.stdout.write(
                    self.style.SUCCESS('Successfully deleted existing topics')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('Failed to delete some topics')
                )
                if not options['ignore_errors']:
                    sys.exit(1)
        
        self.stdout.write('Creating Kafka topics...')
        
        try:
            if topic_manager.create_topics():
                self.stdout.write(
                    self.style.SUCCESS('Successfully created Kafka topics')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('Failed to create Kafka topics')
                )
                if not options['ignore_errors']:
                    sys.exit(1)
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during topic creation: {e}')
            )
            if not options['ignore_errors']:
                sys.exit(1)
