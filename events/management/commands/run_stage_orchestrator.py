"""
Django management command to run Stage Orchestrator consumer
"""
from django.core.management.base import BaseCommand
from events.consumers.stage_orchestrator import StageOrchestratorConsumer


class Command(BaseCommand):
    help = 'Run the Stage Orchestrator consumer'

    def handle(self, *args, **options):
        self.stdout.write('Starting Stage Orchestrator consumer...')
        
        consumer = StageOrchestratorConsumer()
        try:
            consumer.start()
        except KeyboardInterrupt:
            self.stdout.write('\nStopping Stage Orchestrator consumer...')
        finally:
            consumer.shutdown()
            self.stdout.write(
                self.style.SUCCESS('Stage Orchestrator consumer stopped')
            )
