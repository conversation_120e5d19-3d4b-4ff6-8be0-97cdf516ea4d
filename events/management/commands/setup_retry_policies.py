"""
Management command to set up initial retry policies
"""
from django.core.management.base import BaseCommand
from events.models import RetryPolicy


class Command(BaseCommand):
    help = 'Set up initial retry policies for event processing'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all existing retry policies'
        )
    
    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('🔄 Resetting existing retry policies...')
            RetryPolicy.objects.all().delete()
        
        self.stdout.write('🚀 Setting up retry policies...')
        
        # Default policy for all stage failures
        default_policy, created = RetryPolicy.objects.get_or_create(
            event_type='STAGE_FAILED',
            stage=None,
            defaults={
                'max_retries': 3,
                'initial_delay_seconds': 60,  # 1 minute
                'backoff_multiplier': 2.0,
                'max_delay_seconds': 1800,    # 30 minutes
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(f'  ✅ Created default policy: {default_policy}')
        else:
            self.stdout.write(f'  ⚠️  Default policy already exists: {default_policy}')
        
        # Stage-specific policies with different strategies
        stage_policies = [
            # External API stages - more retries with longer delays
            {
                'stage': 'script_generation',
                'max_retries': 5,
                'initial_delay_seconds': 120,  # 2 minutes
                'backoff_multiplier': 1.5,
                'max_delay_seconds': 3600,     # 1 hour
            },
            {
                'stage': 'voice_generation', 
                'max_retries': 5,
                'initial_delay_seconds': 180,  # 3 minutes
                'backoff_multiplier': 1.5,
                'max_delay_seconds': 3600,     # 1 hour
            },
            {
                'stage': 'caption_generation',
                'max_retries': 4,
                'initial_delay_seconds': 90,   # 1.5 minutes
                'backoff_multiplier': 2.0,
                'max_delay_seconds': 2400,     # 40 minutes
            },
            {
                'stage': 'image_prompt_generation',
                'max_retries': 4,
                'initial_delay_seconds': 120,  # 2 minutes
                'backoff_multiplier': 1.5,
                'max_delay_seconds': 1800,     # 30 minutes
            },
            {
                'stage': 'image_generation',
                'max_retries': 6,
                'initial_delay_seconds': 300,  # 5 minutes
                'backoff_multiplier': 1.8,
                'max_delay_seconds': 7200,     # 2 hours
            },
            {
                'stage': 'video_composition',
                'max_retries': 4,
                'initial_delay_seconds': 240,  # 4 minutes
                'backoff_multiplier': 2.0,
                'max_delay_seconds': 3600,     # 1 hour
            },
            
            # Internal stages - fewer retries, shorter delays
            {
                'stage': 'track_creation',
                'max_retries': 2,
                'initial_delay_seconds': 30,   # 30 seconds
                'backoff_multiplier': 2.0,
                'max_delay_seconds': 300,      # 5 minutes
            },
            {
                'stage': 'clip_creation',
                'max_retries': 2,
                'initial_delay_seconds': 30,   # 30 seconds
                'backoff_multiplier': 2.0,
                'max_delay_seconds': 300,      # 5 minutes
            }
        ]
        
        for policy_config in stage_policies:
            stage = policy_config['stage']
            
            policy, created = RetryPolicy.objects.get_or_create(
                event_type='STAGE_FAILED',
                stage=stage,
                defaults={
                    'max_retries': policy_config['max_retries'],
                    'initial_delay_seconds': policy_config['initial_delay_seconds'],
                    'backoff_multiplier': policy_config['backoff_multiplier'],
                    'max_delay_seconds': policy_config['max_delay_seconds'],
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(f'  ✅ Created policy for {stage}: {policy}')
            else:
                self.stdout.write(f'  ⚠️  Policy for {stage} already exists: {policy}')
        
        # N8N specific policies
        n8n_policies = [
            {
                'event_type': 'N8N_STAGE_REQUESTED',
                'stage': None,
                'max_retries': 3,
                'initial_delay_seconds': 180,  # 3 minutes
                'backoff_multiplier': 2.0,
                'max_delay_seconds': 1800,     # 30 minutes
            },
            {
                'event_type': 'N8N_CALL_FAILED',
                'stage': None,
                'max_retries': 2,
                'initial_delay_seconds': 300,  # 5 minutes
                'backoff_multiplier': 2.5,
                'max_delay_seconds': 3600,     # 1 hour
            }
        ]
        
        for policy_config in n8n_policies:
            policy, created = RetryPolicy.objects.get_or_create(
                event_type=policy_config['event_type'],
                stage=policy_config.get('stage'),
                defaults={
                    'max_retries': policy_config['max_retries'],
                    'initial_delay_seconds': policy_config['initial_delay_seconds'],
                    'backoff_multiplier': policy_config['backoff_multiplier'],
                    'max_delay_seconds': policy_config['max_delay_seconds'],
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(f'  ✅ Created policy for {policy_config["event_type"]}: {policy}')
            else:
                self.stdout.write(f'  ⚠️  Policy for {policy_config["event_type"]} already exists: {policy}')
        
        self.stdout.write('\n📊 Retry Policy Summary:')
        policies = RetryPolicy.objects.filter(is_active=True).order_by('event_type', 'stage')
        
        for policy in policies:
            stage_info = f" - {policy.stage}" if policy.stage else " (default)"
            delay_info = f"{policy.initial_delay_seconds}s → {policy.max_delay_seconds}s"
            self.stdout.write(f'  🔧 {policy.event_type}{stage_info}: {policy.max_retries} retries, {delay_info}')
        
        self.stdout.write(f'\n✅ Successfully set up {policies.count()} retry policies!')
        
        # Show example delay calculations
        self.stdout.write('\n⏱️  Example delay calculations (caption_generation):')
        caption_policy = RetryPolicy.objects.filter(stage='caption_generation').first()
        if caption_policy:
            for i in range(caption_policy.max_retries + 1):
                delay = caption_policy.calculate_delay(i)
                self.stdout.write(f'    Retry {i + 1}: {delay} seconds ({delay // 60}m {delay % 60}s)')
        
        self.stdout.write('\n🎯 Retry policies are now configured!')
        self.stdout.write('   Use these commands to manage them:')
        self.stdout.write('   • python manage.py setup_retry_policies --reset  (reset and recreate)')
        self.stdout.write('   • python manage.py validate_providers           (check provider status)')
