# Generated by Django 5.2.1 on 2025-09-04 07:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('videos', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventStore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_id', models.UUIDField(db_index=True, unique=True)),
                ('event_type', models.CharField(db_index=True, max_length=100)),
                ('video_id', models.IntegerField(db_index=True)),
                ('correlation_id', models.CharField(db_index=True, max_length=100)),
                ('event_data', models.JSONField()),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('published', 'Published'), ('failed', 'Failed')], db_index=True, default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('retry_count', models.IntegerField(default=0)),
                ('error_message', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'event_store',
                'ordering': ['created_at'],
                'indexes': [models.Index(fields=['status', 'created_at'], name='event_store_status_0fada7_idx'), models.Index(fields=['video_id', 'event_type'], name='event_store_video_i_2e3fff_idx'), models.Index(fields=['correlation_id', 'created_at'], name='event_store_correla_c20f48_idx')],
            },
        ),
        migrations.CreateModel(
            name='RetryPolicy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(db_index=True, max_length=100)),
                ('stage', models.CharField(blank=True, db_index=True, max_length=50, null=True)),
                ('max_retries', models.IntegerField(default=3)),
                ('initial_delay_seconds', models.IntegerField(default=60)),
                ('backoff_multiplier', models.FloatField(default=2.0)),
                ('max_delay_seconds', models.IntegerField(default=3600)),
                ('is_active', models.BooleanField(db_index=True, default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'retry_policy',
                'indexes': [models.Index(fields=['event_type', 'is_active'], name='retry_polic_event_t_1d2e4a_idx')],
                'unique_together': {('event_type', 'stage')},
            },
        ),
        migrations.CreateModel(
            name='VideoEventLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('video_id', models.IntegerField(db_index=True)),
                ('event_type', models.CharField(db_index=True, max_length=100)),
                ('stage', models.CharField(blank=True, db_index=True, max_length=50, null=True)),
                ('correlation_id', models.CharField(db_index=True, max_length=100)),
                ('event_data', models.JSONField(default=dict)),
                ('status', models.CharField(db_index=True, max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('processing_duration_ms', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'video_event_log',
                'ordering': ['created_at'],
                'indexes': [models.Index(fields=['video_id', 'created_at'], name='video_event_video_i_b910ba_idx'), models.Index(fields=['correlation_id', 'created_at'], name='video_event_correla_2a221f_idx'), models.Index(fields=['stage', 'status'], name='video_event_stage_7ae331_idx')],
            },
        ),
        migrations.CreateModel(
            name='WorkflowState',
            fields=[
                ('video_id', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, related_name='workflow_state', serialize=False, to='videos.video')),
                ('correlation_id', models.CharField(db_index=True, max_length=100)),
                ('current_stage', models.CharField(db_index=True, max_length=50)),
                ('stages_completed', models.JSONField(default=list)),
                ('stages_failed', models.JSONField(default=list)),
                ('retry_counts', models.JSONField(default=dict)),
                ('is_paused', models.BooleanField(db_index=True, default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_activity_at', models.DateTimeField(auto_now=True, db_index=True)),
            ],
            options={
                'db_table': 'workflow_state',
                'indexes': [models.Index(fields=['current_stage', 'is_paused'], name='workflow_st_current_55a4c9_idx'), models.Index(fields=['last_activity_at'], name='workflow_st_last_ac_05d55c_idx')],
            },
        ),
    ]
