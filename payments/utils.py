from datetime import datetime, timedelta
from django.utils import timezone
from .models import UserPackage, Package
from .enums import PackageLimits, PackageFeatures


def get_billing_period_dates(user_package):
    """
    Calculate billing period start and end dates for a user package
    
    Returns:
        tuple: (period_start, period_end)
    """
    period_start = user_package.get_current_billing_period_start()
    
    if user_package.package.billing_cycle == 'monthly':
        period_end = period_start + timedelta(days=30)
    elif user_package.package.billing_cycle == 'yearly':
        period_end = period_start + timedelta(days=365)
    else:  # one_time
        period_end = user_package.end_at or (period_start + timedelta(days=365))
    
    return period_start, period_end


def format_usage_summary(usage_summary, include_percentages=True):
    """
    Format usage summary for display
    
    Args:
        usage_summary: Dictionary from UsageService.get_usage_summary()
        include_percentages: Whether to include percentage information
    
    Returns:
        dict: Formatted usage information
    """
    formatted = {}
    
    for usage_type, data in usage_summary.items():
        used = data.get('used', 0)
        limit = data.get('limit', 0)
        remaining = data.get('remaining', 0)
        percentage = data.get('percentage', 0)
        
        formatted_entry = {
            'type': usage_type,
            'used': used,
            'limit': limit,
            'remaining': remaining,
            'status': 'ok' if percentage < 80 else 'warning' if percentage < 95 else 'critical'
        }
        
        if include_percentages:
            formatted_entry['percentage'] = round(percentage, 1)
            formatted_entry['progress_bar'] = '█' * int(percentage / 10) + '░' * (10 - int(percentage / 10))
        
        # Add human-readable descriptions
        if usage_type == PackageLimits.VIDEO_SECONDS.value:
            formatted_entry['display_name'] = 'Video Duration'
            formatted_entry['unit'] = 'seconds'
            formatted_entry['display_used'] = f"{used // 60}m {used % 60}s"
            formatted_entry['display_limit'] = f"{limit // 60}m {limit % 60}s"
            formatted_entry['display_remaining'] = f"{remaining // 60}m {remaining % 60}s"
        else:
            formatted_entry['display_name'] = usage_type.replace('_', ' ').title()
            formatted_entry['unit'] = 'units'
            formatted_entry['display_used'] = str(used)
            formatted_entry['display_limit'] = str(limit)
            formatted_entry['display_remaining'] = str(remaining)
        
        formatted[usage_type] = formatted_entry
    
    return formatted


def calculate_upgrade_suggestions(user_package, exceeded_usage_type=None, required_amount=None):
    """
    Calculate upgrade suggestions for a user based on their usage patterns
    
    Args:
        user_package: UserPackage instance
        exceeded_usage_type: Usage type that was exceeded (optional)
        required_amount: Amount required for the exceeded usage (optional)
    
    Returns:
        list: List of upgrade suggestions
    """
    from .services import UsageService
    
    current_package = user_package.package
    usage_summary = UsageService.get_usage_summary(user_package)
    
    # Find suitable packages for upgrade
    suitable_packages = Package.objects.filter(
        is_active=True,
        price__gt=current_package.price
    ).order_by('price')
    
    suggestions = []
    
    for package in suitable_packages:
        suggestion = {
            'package_id': package.id,
            'package_name': package.name,
            'price': package.price,
            'price_difference': package.price - current_package.price,
            'billing_cycle': package.billing_cycle,
            'benefits': [],
            'addresses_issues': [],
            'priority_score': 0
        }
        
        # Check what benefits this package provides
        for usage_type, current_data in usage_summary.items():
            package_limit = package.get_limit(usage_type)
            current_limit = current_data.get('limit', 0)
            
            if package_limit > current_limit:
                benefit = {
                    'feature': usage_type,
                    'current_limit': current_limit,
                    'new_limit': package_limit,
                    'improvement': package_limit - current_limit
                }
                suggestion['benefits'].append(benefit)
                
                # Increase priority if this addresses current usage issues
                if current_data.get('percentage', 0) > 80:
                    suggestion['priority_score'] += 2
                    suggestion['addresses_issues'].append({
                        'issue': f"High {usage_type} usage",
                        'current_usage': current_data.get('percentage', 0)
                    })
                
                # Higher priority if this addresses the specific exceeded usage
                if exceeded_usage_type and usage_type == exceeded_usage_type:
                    suggestion['priority_score'] += 5
                    if required_amount and package_limit >= required_amount:
                        suggestion['addresses_issues'].append({
                            'issue': f"Resolves {exceeded_usage_type} limit",
                            'required': required_amount,
                            'provides': package_limit
                        })
        
        # Check feature benefits
        current_features = current_package.features.get('features', {})
        package_features = package.features.get('features', {})
        
        for feature_name, feature_config in package_features.items():
            current_feature = current_features.get(feature_name, {})
            
            if (feature_config.get('enabled', False) and 
                not current_feature.get('enabled', False)):
                suggestion['benefits'].append({
                    'feature': feature_name,
                    'type': 'new_feature',
                    'description': f"Unlocks {feature_name.replace('_', ' ')}"
                })
                suggestion['priority_score'] += 1
        
        # Calculate recommendation strength
        if suggestion['priority_score'] >= 5:
            suggestion['recommendation'] = 'highly_recommended'
        elif suggestion['priority_score'] >= 3:
            suggestion['recommendation'] = 'recommended'
        elif suggestion['priority_score'] >= 1:
            suggestion['recommendation'] = 'consider'
        else:
            suggestion['recommendation'] = 'future_option'
        
        suggestions.append(suggestion)
    
    # Sort by priority score and price
    suggestions.sort(key=lambda x: (-x['priority_score'], x['price']))
    
    return suggestions[:3]  # Return top 3 suggestions


def validate_multiple_limits(user_package, limits_to_check):
    """
    Check multiple limits simultaneously
    
    Args:
        user_package: UserPackage instance
        limits_to_check: Dict of {usage_type: amount} to check
    
    Returns:
        dict: Validation results
    """
    results = {
        'all_valid': True,
        'violations': [],
        'warnings': [],
        'summary': {}
    }
    
    for usage_type, amount in limits_to_check.items():
        can_use = user_package.can_use_feature(usage_type, amount)
        current_usage = user_package.get_usage_for_period(usage_type)
        limit = user_package.package.get_limit(usage_type)
        remaining = user_package.get_remaining_limit(usage_type)
        
        result = {
            'usage_type': usage_type,
            'requested_amount': amount,
            'can_use': can_use,
            'current_usage': current_usage,
            'limit': limit,
            'remaining': remaining,
            'would_exceed': not can_use
        }
        
        if not can_use:
            results['all_valid'] = False
            results['violations'].append(result)
        elif remaining - amount < limit * 0.1:  # Less than 10% remaining after use
            results['warnings'].append(result)
        
        results['summary'][usage_type] = result
    
    return results


def get_package_comparison(current_package, compare_package):
    """
    Compare two packages and highlight differences
    
    Returns:
        dict: Comparison details
    """
    comparison = {
        'price_difference': compare_package.price - current_package.price,
        'limit_improvements': {},
        'new_features': [],
        'feature_improvements': {}
    }
    
    # Compare limits
    current_limits = current_package.features.get('limits', {})
    compare_limits = compare_package.features.get('limits', {})
    
    for limit_type, compare_limit in compare_limits.items():
        current_limit = current_limits.get(limit_type, 0)
        if compare_limit > current_limit:
            comparison['limit_improvements'][limit_type] = {
                'current': current_limit,
                'new': compare_limit,
                'improvement': compare_limit - current_limit,
                'multiplier': compare_limit / current_limit if current_limit > 0 else float('inf')
            }
    
    # Compare features
    current_features = current_package.features.get('features', {})
    compare_features = compare_package.features.get('features', {})
    
    for feature_name, feature_config in compare_features.items():
        current_feature = current_features.get(feature_name, {})
        
        if (feature_config.get('enabled', False) and 
            not current_feature.get('enabled', False)):
            comparison['new_features'].append(feature_name)
        elif (feature_config.get('enabled', False) and 
              current_feature.get('enabled', False)):
            # Check for feature improvements
            if isinstance(feature_config, dict) and isinstance(current_feature, dict):
                improvements = {}
                for key, value in feature_config.items():
                    if key != 'enabled' and key in current_feature:
                        if value != current_feature[key]:
                            improvements[key] = {
                                'current': current_feature[key],
                                'new': value
                            }
                if improvements:
                    comparison['feature_improvements'][feature_name] = improvements
    
    return comparison


def estimate_monthly_cost_savings(user_package, target_package):
    """
    Estimate potential cost savings or additional costs
    """
    current_monthly_cost = user_package.package.price
    target_monthly_cost = target_package.price
    
    # Convert yearly to monthly for comparison
    if user_package.package.billing_cycle == 'yearly':
        current_monthly_cost = current_monthly_cost / 12
    if target_package.billing_cycle == 'yearly':
        target_monthly_cost = target_monthly_cost / 12
    
    difference = target_monthly_cost - current_monthly_cost
    
    return {
        'current_monthly': current_monthly_cost,
        'target_monthly': target_monthly_cost,
        'difference': difference,
        'is_increase': difference > 0,
        'percentage_change': (difference / current_monthly_cost * 100) if current_monthly_cost > 0 else 0
    }


def get_usage_trend_analysis(user_package, usage_type, days=30):
    """
    Analyze usage trends for predictions and recommendations
    """
    from .services import UsageService
    
    usage_stats = UsageService.get_feature_usage_stats(user_package, usage_type, days)
    
    if not usage_stats:
        return {'trend': 'no_data'}
    
    # Calculate trend
    daily_totals = {}
    for amount, created_at in usage_stats:
        date_key = created_at.date()
        daily_totals[date_key] = daily_totals.get(date_key, 0) + amount
    
    if len(daily_totals) < 7:
        return {'trend': 'insufficient_data'}
    
    # Calculate moving average
    sorted_dates = sorted(daily_totals.keys())
    recent_week = sorted_dates[-7:]
    older_week = sorted_dates[-14:-7] if len(sorted_dates) >= 14 else sorted_dates[:-7]
    
    recent_avg = sum(daily_totals.get(date, 0) for date in recent_week) / len(recent_week)
    older_avg = sum(daily_totals.get(date, 0) for date in older_week) / len(older_week) if older_week else recent_avg
    
    trend_direction = 'increasing' if recent_avg > older_avg * 1.1 else 'decreasing' if recent_avg < older_avg * 0.9 else 'stable'
    
    return {
        'trend': trend_direction,
        'recent_daily_average': recent_avg,
        'older_daily_average': older_avg,
        'change_percentage': ((recent_avg - older_avg) / older_avg * 100) if older_avg > 0 else 0,
        'prediction': UsageService.predict_usage_exhaustion(user_package, usage_type)
    }