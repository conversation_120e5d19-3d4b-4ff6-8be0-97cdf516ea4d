from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.conf import settings
from django.utils import timezone
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json
import logging
from datetime import timedelta

# Import all models (existing + enhanced)
from .models import (
    Package, UserPackage, PaymentHistory, PaymentSession
)

# Import all serializers (existing + enhanced)
from .serializers import (
    PackageSerializer, UserPackageSerializer, PaymentHistorySerializer,
    PaymentSessionSerializer, 
    CreatePaymentSessionSerializer
)

# Import services
from .services import UsageService, PaymentSessionService
from .utils import format_usage_summary, calculate_upgrade_suggestions
from .enums import PackageLimits, PackageFeatures

# Import enhanced components if they exist
try:
    from .gateways.factory import PaymentGatewayFactory
except ImportError:
    PaymentGatewayFactory = None


logger = logging.getLogger(__name__)


class PackageViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing available packages with gateway information.
    """
    queryset = Package.objects.filter(is_active=True)
    serializer_class = PackageSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def with_payment_methods(self, request):
        """Get packages with available payment methods"""
        packages = self.get_queryset()
        
        # Get available gateways if factory is available
        if PaymentGatewayFactory:
            try:
                available_gateways = PaymentGatewayFactory.get_available_gateways()
                
                # Get supported payment methods
                supported_methods = set()
                for gateway_info in available_gateways.values():
                    if gateway_info.get('available'):
                        supported_methods.update(gateway_info.get('supported_methods', []))
            except Exception as e:
                logger.error(f"Error getting gateway info: {str(e)}")
                available_gateways = {}
                supported_methods = set()
        else:
            available_gateways = {}
            supported_methods = set()
        
        packages_data = []
        for package in packages:
            package_data = PackageSerializer(package).data
            package_data['supported_payment_methods'] = list(supported_methods)
            package_data['available_gateways'] = available_gateways
            packages_data.append(package_data)
        
        return Response({
            'packages': packages_data,
            'total_gateways': len(available_gateways),
            'supported_methods': list(supported_methods)
        })


class UserPackageViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing user's subscribed packages.
    """
    serializer_class = UserPackageSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """
        Return packages for the current authenticated user.
        """
        return UserPackage.objects.filter(user=self.request.user).order_by('-created_at')


class PaymentHistoryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing payment history.
    """
    serializer_class = PaymentHistorySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """
        Return payment history for the current authenticated user.
        """
        return PaymentHistory.objects.filter(user=self.request.user).order_by('-created_at')


class UsageAnalyticsViewSet(viewsets.ViewSet):
    """
    API endpoint for usage analytics and dashboard
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def list(self, request):
        """Get comprehensive usage analytics for the user"""
        user_package = request.user.user_packages.filter(status='active').first()
        if not user_package:
            return Response({
                'error': 'No active subscription found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get usage summary
        usage_summary = UsageService.get_usage_summary(user_package)
        formatted_usage = format_usage_summary(usage_summary)
        
        # Get billing period info
        period_start, period_end = user_package.get_current_billing_period_start(), None
        if user_package.package.billing_cycle == 'monthly':
            period_end = period_start + timedelta(days=30)
        elif user_package.package.billing_cycle == 'yearly':
            period_end = period_start + timedelta(days=365)
        
        # Calculate days remaining safely
        days_remaining = None
        if period_end:
            time_diff = period_end - timezone.now()
            days_remaining = int(time_diff.days) if hasattr(time_diff, 'days') else None
        
        # Get usage trends for the last 30 days
        usage_trends = {}
        for usage_type in [PackageLimits.VIDEO_SECONDS.value]:
            analytics = UsageService.get_usage_analytics(user_package, usage_type, 30)
            usage_trends[usage_type] = analytics
        
        # Get predictions
        predictions = {}
        for usage_type in [PackageLimits.VIDEO_SECONDS.value]:
            prediction = UsageService.predict_usage_exhaustion(user_package, usage_type)
            predictions[usage_type] = prediction
        
        return Response({
            'package_info': {
                'name': user_package.package.name,
                'price': user_package.package.price,
                'billing_cycle': user_package.package.billing_cycle,
                'status': user_package.status
            },
            'billing_period': {
                'start_date': period_start.isoformat(),
                'end_date': period_end.isoformat() if period_end else None,
                'days_remaining': days_remaining
            },
            'usage_summary': formatted_usage,
            'usage_trends': usage_trends,
            'predictions': predictions,
            'limits': user_package.package.features.get('limits', {}),
            'features': user_package.package.features.get('features', {})
        })
    
    @action(detail=False, methods=['get'])
    def current_usage(self, request):
        """Get current usage summary only"""
        user_package = request.user.user_packages.filter(status='active').first()
        if not user_package:
            return Response({
                'error': 'No active subscription found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        usage_summary = UsageService.get_usage_summary(user_package)
        return Response({
            'usage_summary': usage_summary,
            'package_name': user_package.package.name
        })
    
    @action(detail=False, methods=['post'])
    def check_limits(self, request):
        """Check multiple usage limits at once"""
        user_package = request.user.user_packages.filter(status='active').first()
        if not user_package:
            return Response({
                'error': 'No active subscription found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get limits to check from request
        limits_to_check = request.data.get('limits', {})
        
        if not limits_to_check:
            return Response({
                'error': 'No limits specified to check'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        from .utils import validate_multiple_limits
        validation_result = validate_multiple_limits(user_package, limits_to_check)
        
        return Response(validation_result)


class UpgradeRecommendationView(APIView):
    """
    API endpoint for upgrade recommendations
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get upgrade recommendations for the user"""
        user_package = request.user.user_packages.filter(status='active').first()
        if not user_package:
            return Response({
                'error': 'No active subscription found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get upgrade suggestions
        suggestions = calculate_upgrade_suggestions(user_package)
        
        # Get usage summary for context
        usage_summary = UsageService.get_usage_summary(user_package)
        
        # Identify usage issues
        issues = []
        for usage_type, data in usage_summary.items():
            percentage = data.get('percentage', 0)
            if percentage >= 95:
                issues.append({
                    'type': 'critical',
                    'usage_type': usage_type,
                    'percentage': percentage,
                    'message': f'Critical: {usage_type.replace("_", " ")} usage at {percentage:.1f}%'
                })
            elif percentage >= 80:
                issues.append({
                    'type': 'warning',
                    'usage_type': usage_type,
                    'percentage': percentage,
                    'message': f'Warning: {usage_type.replace("_", " ")} usage at {percentage:.1f}%'
                })
        
        return Response({
            'current_package': {
                'name': user_package.package.name,
                'price': user_package.package.price,
                'billing_cycle': user_package.package.billing_cycle
            },
            'usage_issues': issues,
            'upgrade_suggestions': suggestions,
            'total_suggestions': len(suggestions)
        })
    
    def post(self, request):
        """Get recommendations for specific usage scenario"""
        user_package = request.user.user_packages.filter(status='active').first()
        if not user_package:
            return Response({
                'error': 'No active subscription found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get scenario from request
        exceeded_usage_type = request.data.get('exceeded_usage_type')
        required_amount = request.data.get('required_amount')
        
        suggestions = calculate_upgrade_suggestions(
            user_package, 
            exceeded_usage_type, 
            required_amount
        )
        
        return Response({
            'scenario': {
                'exceeded_usage_type': exceeded_usage_type,
                'required_amount': required_amount
            },
            'suggestions': suggestions
        })


# Enhanced Payment System Views

class PaymentSessionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing payment sessions
    """
    serializer_class = PaymentSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'session_id'
    
    def get_queryset(self):
        """Return payment sessions for the current authenticated user"""
        return PaymentSession.objects.filter(user=self.request.user).order_by('-created_at')
    
    @action(detail=False, methods=['post'])
    def create_session(self, request):
        """Create a new payment session with proper gateway integration"""
        serializer = CreatePaymentSessionSerializer(data=request.data)
        
        if not serializer.is_valid():
            return Response({
                'success': False,
                'error': 'Invalid request data',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        package_id = serializer.validated_data['package_id']
        gateway_name = serializer.validated_data.get('gateway_name', 'auto')
        callback_url = serializer.validated_data.get('callback_url', '')
        metadata = serializer.validated_data.get('metadata', {})

        # Get the package
        try:
            package = Package.objects.get(id=package_id, is_active=True)
        except Package.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Package not found or inactive'
            }, status=status.HTTP_404_NOT_FOUND)

        # Check if user already has an active subscription for this package
        existing_subscription = UserPackage.objects.filter(
            user=request.user,
            package=package,
            status='active'
        ).first()
        
        if existing_subscription:
            return Response({
                'success': False,
                'error': 'You already have an active subscription for this package',
                'existing_subscription': {
                    'id': existing_subscription.id,
                    'started_at': existing_subscription.started_at.isoformat(),
                    'status': existing_subscription.status
                }
            }, status=status.HTTP_400_BAD_REQUEST)

        # For free packages (price = 0), automatically activate without payment
        if package.price == 0:
            # Check if user already has this free package
            if existing_subscription:
                return Response({
                    'success': True,
                    'message': 'Free package already active',
                    'package_activated': True,
                    'user_package_id': existing_subscription.id,
                    'package_name': package.name,
                    'started_at': existing_subscription.started_at.isoformat()
                }, status=status.HTTP_200_OK)
            
            user_package = UserPackage.objects.create(
                user=request.user,
                package=package,
                status='active',
                started_at=timezone.now()
            )
            
            return Response({
                'success': True,
                'message': 'Free package activated successfully',
                'package_activated': True,
                'user_package_id': user_package.id,
                'package_name': package.name,
                'started_at': user_package.started_at.isoformat(),
                'amount': 0.00,
                'currency': 'USD'
            }, status=status.HTTP_201_CREATED)

        # Create payment session using enhanced service
        result = PaymentSessionService.create_payment_session(
            user=request.user,
            package=package,
            gateway_name=gateway_name,
            callback_url=callback_url,
            metadata=metadata
        )

        if result['success']:
            return Response(result, status=status.HTTP_201_CREATED)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def verify(self, request, session_id=None):
        """Verify payment completion for a session with proper gateway integration"""
        try:
            payment_session = self.get_object()
            gateway_data = request.data.get('gateway_data', {})

            # Use enhanced verification service
            result = PaymentSessionService.verify_payment_session(
                payment_session.session_id, 
                gateway_data
            )

            if result['success']:
                return Response(result, status=status.HTTP_200_OK)
            else:
                return Response(result, status=status.HTTP_400_BAD_REQUEST)

        except PaymentSession.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Payment session not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'error': 'Verification failed',
                'details': str(e) if settings.DEBUG else None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['get'])
    def status(self, request, session_id=None):
        """Get current payment session status with enhanced information"""
        try:
            payment_session = self.get_object()
            
            # Check if session is expired
            is_expired = False
            if hasattr(payment_session, 'expires_at') and payment_session.expires_at:
                is_expired = timezone.now() > payment_session.expires_at

            # Get gateway-specific status if needed
            gateway_status = None
            if payment_session.gateway_session_id and payment_session.gateway_name not in ['free']:
                try:
                    from .gateways.factory import PaymentGatewayFactory
                    gateway = PaymentGatewayFactory.create_gateway(payment_session.gateway_name)
                    gateway_status_response = gateway.get_payment_status(payment_session.gateway_session_id)
                    gateway_status = {
                        'gateway_status': gateway_status_response.status.value if hasattr(gateway_status_response, 'status') else None,
                        'gateway_success': getattr(gateway_status_response, 'success', None)
                    }
                except Exception as e:
                    logger.warning(f"Failed to get gateway status: {str(e)}")

            response_data = {
                'session_id': payment_session.session_id,
                'status': payment_session.status,
                'gateway_name': payment_session.gateway_name,
                'gateway_session_id': payment_session.gateway_session_id,
                'amount': float(payment_session.amount),
                'currency': payment_session.currency,
                'is_expired': is_expired,
                'created_at': payment_session.created_at.isoformat(),
                'package': {
                    'id': payment_session.package.id,
                    'name': payment_session.package.name,
                    'price': float(payment_session.package.price),
                    'billing_cycle': payment_session.package.billing_cycle
                }
            }
            
            if hasattr(payment_session, 'expires_at') and payment_session.expires_at:
                response_data['expires_at'] = payment_session.expires_at.isoformat()
            
            if gateway_status:
                response_data['gateway_status'] = gateway_status

            return Response(response_data)

        except PaymentSession.DoesNotExist:
            return Response({
                'error': 'Payment session not found'
            }, status=status.HTTP_404_NOT_FOUND)


class PaymentGatewayInfoView(APIView):
    """
    API endpoint for payment gateway information
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get information about available payment gateways"""
        
        # Default gateway info if factory is not available
        default_gateways = {
            'razorpay': {
                'name': 'Razorpay',
                'available': hasattr(settings, 'RAZORPAY_KEY_ID'),
                'supported_methods': ['card', 'upi', 'net_banking', 'wallet'],
                'test_mode': settings.DEBUG
            }
        }
        
        if PaymentGatewayFactory:
            try:
                available_gateways = PaymentGatewayFactory.get_available_gateways()
                connection_tests = PaymentGatewayFactory.test_gateway_connections()
                
                # Combine information
                gateway_info = {}
                for gateway_name, info in available_gateways.items():
                    gateway_info[gateway_name] = {
                        **info,
                        'connection_test': connection_tests.get(gateway_name, {})
                    }
            except Exception as e:
                logger.error(f"Error getting gateway info: {str(e)}")
                gateway_info = default_gateways
        else:
            gateway_info = default_gateways
        
        return Response({
            'gateways': gateway_info,
            'default_gateway': getattr(settings, 'DEFAULT_PAYMENT_GATEWAY', 'razorpay'),
            'test_mode': settings.DEBUG
        })


@method_decorator(csrf_exempt, name='dispatch')
class PaymentWebhookView(APIView):
    """
    Universal webhook endpoint for all payment gateways
    """
    permission_classes = [permissions.AllowAny]
    
    def post(self, request, gateway_name):
        """Handle webhook from specific gateway"""
        
        try:
            # Get signature from headers
            signature = (
                request.META.get('HTTP_X_RAZORPAY_SIGNATURE') or
                request.META.get('HTTP_X_WEBHOOK_SIGNATURE', '')
            )
            
            # Get payload
            if hasattr(request, 'data') and request.data:
                payload = request.data
            else:
                payload = json.loads(request.body.decode('utf-8'))
            
            # Log webhook received
            logger.info(f"Webhook received from {gateway_name}: {payload}")
            
            # Simple webhook processing (can be enhanced later)
            if gateway_name == 'razorpay':
                # Handle Razorpay webhook
                session_id = payload.get('session_id')
                if session_id:
                    try:
                        payment_session = PaymentSession.objects.get(session_id=session_id)
                        payment_session.status = 'completed' if payload.get('status') == 'success' else 'failed'
                        payment_session.save()
                    except PaymentSession.DoesNotExist:
                        logger.error(f"Payment session not found: {session_id}")
            
            return HttpResponse(status=200)
                
        except Exception as e:
            logger.error(f"Error processing webhook from {gateway_name}: {str(e)}")
            return HttpResponse(status=500)


class PaymentMethodsView(APIView):
    """
    API endpoint for getting supported payment methods
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get supported payment methods for user's location"""
        
        gateway_name = request.query_params.get('gateway', 'razorpay')
        
        # Default payment methods
        default_methods = {
            'razorpay': ['card', 'upi', 'net_banking', 'wallet'],
            'stripe': ['card']
        }
        
        supported_methods = default_methods.get(gateway_name, ['card'])
        
        return Response({
            'gateway_name': gateway_name,
            'supported_methods': supported_methods,
            'features': {
                'recurring_payments': gateway_name in ['razorpay', 'stripe'],
                'instant_refunds': gateway_name in ['razorpay'],
                'international_cards': gateway_name in ['stripe']
            },
            'test_mode': settings.DEBUG
        })


class PaymentAnalyticsView(APIView):
    """
    API endpoint for payment analytics and reporting
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get payment analytics for the user"""
        
        user = request.user
        
        # Get payment history
        payment_history = PaymentHistory.objects.filter(user=user).order_by('-created_at')
        
        # Calculate analytics
        total_payments = payment_history.count()
        successful_payments = payment_history.filter(status='succeeded').count()
        total_amount = sum(float(p.amount) for p in payment_history.filter(status='succeeded'))
        
        # Payment method breakdown
        method_breakdown = {}
        for payment in payment_history.filter(status='succeeded'):
            method = getattr(payment, 'payment_method', 'unknown')
            method_breakdown[method] = method_breakdown.get(method, 0) + 1
        
        # Gateway breakdown
        gateway_breakdown = {}
        for payment in payment_history:
            gateway = getattr(payment, 'payment_gateway', 'unknown')
            gateway_breakdown[gateway] = gateway_breakdown.get(gateway, 0) + 1
        
        # Recent payments
        recent_payments = payment_history[:10]
        recent_payments_data = PaymentHistorySerializer(recent_payments, many=True).data
        
        return Response({
            'summary': {
                'total_payments': total_payments,
                'successful_payments': successful_payments,
                'success_rate': (successful_payments / total_payments * 100) if total_payments > 0 else 0,
                'total_amount': total_amount,
                'currency': 'USD'
            },
            'breakdowns': {
                'payment_methods': method_breakdown,
                'gateways': gateway_breakdown
            },
            'recent_payments': recent_payments_data
        })


class SubscriptionManagementView(APIView):
    """
    Enhanced subscription management with multi-gateway support
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """Get current subscription status"""
        
        user = request.user
        active_package = user.user_packages.filter(status='active').first()
        
        if not active_package:
            return Response({
                'has_subscription': False,
                'message': 'No active subscription found'
            })
        
        # Get usage summary
        usage_summary = UsageService.get_usage_summary(active_package)
        
        # Get subscription details
        subscription_data = {
            'has_subscription': True,
            'package': {
                'id': active_package.package.id,
                'name': active_package.package.name,
                'price': float(active_package.package.price),
                'billing_cycle': active_package.package.billing_cycle
            },
            'subscription': {
                'id': active_package.id,
                'status': active_package.status,
                'started_at': active_package.started_at.isoformat(),
                'end_at': active_package.end_at.isoformat() if hasattr(active_package, 'end_at') and active_package.end_at else None,
                'is_trial': getattr(active_package, 'is_trial', False)
            },
            'usage': format_usage_summary(usage_summary),
            'limits': active_package.package.features.get('limits', {}),
            'features': active_package.package.features.get('features', {})
        }
        
        # Get payment gateway used for this subscription
        latest_payment = active_package.payment_history.filter(status='succeeded').first()
        if latest_payment:
            subscription_data['payment_info'] = {
                'gateway': getattr(latest_payment, 'payment_gateway', 'unknown'),
                'payment_method': getattr(latest_payment, 'payment_method', 'unknown'),
                'last_payment_date': latest_payment.created_at.isoformat(),
                'is_test_payment': getattr(latest_payment, 'is_test_payment', False)
            }
        
        return Response(subscription_data)
    
    def delete(self, request):
        """Cancel current subscription"""
        
        user = request.user
        active_package = user.user_packages.filter(status='active').first()
        
        if not active_package:
            return Response({
                'success': False,
                'error': 'No active subscription to cancel'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Update local subscription
            active_package.status = 'cancelled'
            if hasattr(active_package, 'cancelled_at'):
                active_package.cancelled_at = timezone.now()
            active_package.save()
            
            return Response({
                'success': True,
                'message': 'Subscription cancelled successfully',
                'cancelled_at': timezone.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error cancelling subscription for user {user.id}: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to cancel subscription',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
