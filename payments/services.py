from django.utils import timezone
from django.conf import settings
from django.db import transaction
import uuid
import logging
from datetime import timedelta
from .models import UsageTracking, UserPackage, PaymentSession, PaymentHistory, Package
from .enums import PackageLimits, PackageFeatures


logger = logging.getLogger(__name__)


class PaymentSessionService:
    """
    Simplified service for managing payment sessions
    """
    
    @staticmethod
    def create_payment_session(user, package, gateway_name='razorpay', callback_url='', metadata=None):
        """Create a payment session using the actual payment gateway"""
        try:
            from .gateways.factory import PaymentGatewayFactory, PaymentGatewaySelector
            from .gateways.base import PaymentRequest
            
            # Select optimal gateway if none specified or auto
            if not gateway_name or gateway_name == 'auto':
                gateway_name = PaymentGatewaySelector.select_optimal_gateway(
                    amount=float(package.price),
                    currency='USD',
                    user_location='US'  # Could be determined from user profile
                )
            
            # Generate unique session ID
            session_id = str(uuid.uuid4())
            
            # Set expiration time (1 hour from now)
            expires_at = timezone.now() + timedelta(hours=1)
            
            # Prepare callback URL
            if not callback_url:
                base_url = getattr(settings, 'BASE_URL', 'https://yourapp.com')
                callback_url = f"{base_url}/payment/callback"
            
            # Prepare metadata
            gateway_metadata = metadata or {}
            gateway_metadata.update({
                'user_id': str(user.id),
                'package_id': str(package.id),
                'billing_cycle': package.billing_cycle,
                'session_id': session_id,
                'callback_url': callback_url
            })
            
            # For free packages, skip gateway and create session directly
            if package.price == 0:
                payment_session = PaymentSession.objects.create(
                    session_id=session_id,
                    user=user,
                    package=package,
                    amount=package.price,
                    currency='USD',
                    gateway_name='free',
                    status='completed',
                    expires_at=expires_at,
                    gateway_metadata=gateway_metadata
                )
                
                return {
                    'success': True,
                    'session_id': payment_session.session_id,
                    'payment_url': None,  # No payment needed
                    'gateway_session_id': None,
                    'amount': float(payment_session.amount),
                    'currency': payment_session.currency,
                    'gateway_name': 'free',
                    'is_free': True,
                    'expires_at': payment_session.expires_at.isoformat()
                }
            
            # Create payment request for paid packages
            payment_request = PaymentRequest(
                amount=float(package.price),
                currency='USD',
                user_id=str(user.id),
                package_id=str(package.id),
                callback_url=callback_url,
                webhook_url=f"{getattr(settings, 'BASE_URL')}/api/payments/webhooks/{gateway_name}/",
                metadata=gateway_metadata,
                description=f"Subscription to {package.name} - {package.billing_cycle}"
            )
            
            # Create gateway instance and payment session
            gateway = PaymentGatewayFactory.create_gateway(gateway_name)
            gateway_response = gateway.create_payment_session(payment_request)
            
            if not gateway_response.success:
                return {
                    'success': False,
                    'error': gateway_response.error_message or 'Failed to create payment session',
                    'error_code': getattr(gateway_response, 'error_code', 'GATEWAY_ERROR')
                }
            
            # Save payment session to database
            payment_session = PaymentSession.objects.create(
                session_id=gateway_response.session_id or session_id,
                user=user,
                package=package,
                amount=package.price,
                currency='USD',
                gateway_name=gateway_name,
                gateway_session_id=gateway_response.gateway_session_id,
                gateway_metadata=getattr(gateway_response, 'gateway_response', {}),
                status='created',
                expires_at=expires_at
            )
            
            return {
                'success': True,
                'session_id': payment_session.session_id,
                'payment_url': gateway_response.payment_url,
                'gateway_session_id': gateway_response.gateway_session_id,
                'amount': float(payment_session.amount),
                'currency': payment_session.currency,
                'gateway_name': gateway_name,
                'gateway_response': getattr(gateway_response, 'gateway_response', {}),
                'expires_at': payment_session.expires_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error creating payment session: {str(e)}")
            return {
                'success': False,
                'error': 'Failed to create payment session',
                'error_details': str(e) if getattr(settings, 'DEBUG', False) else None
            }
    
    @staticmethod
    def verify_payment_session(session_id, gateway_data=None):
        """Verify payment completion using the actual gateway"""
        logger.info(f"Starting payment verification for session: {session_id}")
        logger.debug(f"Gateway data received: {gateway_data}")
        
        try:
            from .gateways.factory import PaymentGatewayFactory
            
            payment_session = PaymentSession.objects.get(session_id=session_id)
            logger.info(f"Found payment session: {payment_session.session_id}, gateway: {payment_session.gateway_name}, status: {payment_session.status}")
            
            # IDEMPOTENCY CHECK: If payment session is already completed, return success
            if payment_session.status == 'completed':
                logger.info(f"Payment session {session_id} is already completed, returning cached result")
                
                # Get existing payment history and user package
                try:
                    payment_history = PaymentHistory.objects.filter(
                        payment_session=payment_session,
                        status='succeeded'
                    ).first()
                    
                    user_package = UserPackage.objects.filter(
                        user=payment_session.user,
                        package=payment_session.package,
                        status='active'
                    ).first()
                    
                    return {
                        'success': True,
                        'message': 'Payment already verified successfully',
                        'session_id': session_id,
                        'status': payment_session.status,
                        'transaction_id': payment_history.gateway_transaction_id if payment_history else '',
                        'user_package_id': user_package.id if user_package else None,
                        'already_processed': True  # Flag to indicate this was already processed
                    }
                except Exception as e:
                    logger.warning(f"Error retrieving cached result for completed session {session_id}: {str(e)}")
                    # Continue with normal processing if we can't get cached result
            
            # Handle free packages
            if payment_session.gateway_name == 'free' or payment_session.amount == 0:
                logger.info(f"Processing free package for session: {session_id}")
                if payment_session.status != 'completed':
                    payment_session.status = 'completed'
                    payment_session.save()
                
                # Create/activate user package
                user_package, created = UserPackage.objects.get_or_create(
                    user=payment_session.user,
                    package=payment_session.package,
                    defaults={
                        'status': 'active',
                        'started_at': timezone.now(),
                        'is_trial': False
                    }
                )
                
                # If package already existed but was inactive, reactivate it
                if not created and user_package.status != 'active':
                    user_package.status = 'active'
                    user_package.started_at = timezone.now()
                    user_package.save()  # This will trigger the end_at calculation
                
                return {
                    'success': True,
                    'message': 'Free package activated successfully',
                    'session_id': session_id,
                    'status': payment_session.status,
                    'user_package_id': user_package.id
                }
            
            # Create gateway instance for paid packages
            logger.info(f"Creating gateway instance for: {payment_session.gateway_name}")
            try:
                gateway = PaymentGatewayFactory.create_gateway(payment_session.gateway_name)
                logger.info(f"Successfully created {payment_session.gateway_name} gateway instance")
            except Exception as gateway_error:
                logger.error(f"Failed to create gateway instance: {str(gateway_error)}")
                raise
            
            # Verify payment
            logger.info(f"Verifying payment with gateway for session: {session_id}")
            logger.debug(f"Gateway data being passed: {gateway_data}")
            verification_response = gateway.verify_payment(session_id, gateway_data)
            logger.info(f"Gateway verification response: success={verification_response.success}, status={verification_response.status}")
            logger.debug(f"Verification response details: {verification_response}")
            
            if hasattr(verification_response, 'error_message') and verification_response.error_message:
                logger.info(f"Gateway error message: {verification_response.error_message}")
            
            if verification_response.success:
                logger.info(f"Payment verification successful for session: {session_id}")
                # Update payment session
                payment_session.status = 'completed'
                payment_session.payment_method = getattr(verification_response, 'payment_method', 'unknown')
                payment_session.gateway_metadata.update({
                    'verification_data': getattr(verification_response, 'gateway_metadata', {}),
                    'gateway_transaction_id': getattr(verification_response, 'gateway_transaction_id', '')
                })
                payment_session.save()
                
                # Create payment history record
                PaymentHistory.objects.create(
                    user=payment_session.user,
                    package=payment_session.package,
                    user_package=None,  # Will be set after UserPackage creation
                    payment_session=payment_session,
                    amount=payment_session.amount,
                    currency=payment_session.currency,
                    status='succeeded',
                    payment_gateway=payment_session.gateway_name,
                    gateway_transaction_id=getattr(verification_response, 'gateway_transaction_id', ''),
                    payment_method=getattr(verification_response, 'payment_method', 'unknown'),
                    gateway_metadata=getattr(verification_response, 'gateway_metadata', {})
                )
                
                # Activate user package
                user_package, created = UserPackage.objects.get_or_create(
                    user=payment_session.user,
                    package=payment_session.package,
                    defaults={
                        'status': 'active',
                        'started_at': timezone.now(),
                        'is_trial': False
                    }
                )
                
                # If package already existed but was inactive, reactivate it
                if not created and user_package.status != 'active':
                    user_package.status = 'active'
                    user_package.started_at = timezone.now()
                    user_package.save()  # This will trigger the end_at calculation
                
                # Update payment history with user_package reference
                PaymentHistory.objects.filter(
                    payment_session=payment_session
                ).update(user_package=user_package)
                
                return {
                    'success': True,
                    'message': 'Payment verified successfully',
                    'session_id': session_id,
                    'status': payment_session.status,
                    'transaction_id': getattr(verification_response, 'gateway_transaction_id', ''),
                    'user_package_id': user_package.id
                }
            else:
                logger.warning(f"Payment verification failed for session: {session_id}")
                if hasattr(verification_response, 'error_message'):
                    logger.warning(f"Verification error message: '{verification_response.error_message}'")
                else:
                    logger.warning("No error_message attribute found in verification response")
                
                error_message = getattr(verification_response, 'error_message', 'Payment verification failed')
                if not error_message or error_message.strip() == '':
                    error_message = 'Payment verification failed - no specific error provided'
                
                logger.warning(f"Using error message: '{error_message}'")
                
                # Update payment session status
                payment_session.status = 'failed'
                payment_session.save()
                
                # Create failed payment history
                PaymentHistory.objects.create(
                    user=payment_session.user,
                    package=payment_session.package,
                    payment_session=payment_session,
                    amount=payment_session.amount,
                    currency=payment_session.currency,
                    status='failed',
                    payment_gateway=payment_session.gateway_name,
                    gateway_metadata={'error': getattr(verification_response, 'error_message', 'Verification failed')}
                )
                
                return {
                    'success': False,
                    'error': error_message,
                    'session_id': session_id,
                    'status': payment_session.status
                }
                
        except PaymentSession.DoesNotExist:
            logger.error(f"Payment session not found: {session_id}")
            return {
                'success': False,
                'error': 'Payment session not found'
            }
        except Exception as e:
            logger.error(f"Error verifying payment session {session_id}: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': 'Payment verification failed',
                'error_details': str(e) if getattr(settings, 'DEBUG', False) else None
            }


class UsageService:
    @staticmethod
    def record_usage(user_package, usage_type, amount, resource_id=None, metadata=None):
        """Record usage for any type of resource"""
        return UsageTracking.objects.create(
            user_package=user_package,
            usage_type=usage_type,
            amount=amount,
            resource_id=resource_id,
            metadata=metadata or {}
        )
    
    @staticmethod
    def can_create_video(user, duration_seconds):
        """Check if user can create a video of given duration"""
        user_package = user.user_packages.filter(status='active').first()
        if not user_package:
            return False, "No active subscription"
        
        # Check video seconds limit
        if not user_package.can_use_feature(PackageLimits.VIDEO_SECONDS.value, duration_seconds):
            remaining = user_package.get_remaining_limit(PackageLimits.VIDEO_SECONDS.value)
            return False, f"Insufficient video seconds. Remaining: {remaining}s"
        
        # Check max single video duration
        max_duration = user_package.package.get_limit(PackageLimits.MAX_SINGLE_VIDEO_SECONDS.value)
        if max_duration > 0 and duration_seconds > max_duration:
            return False, f"Video too long. Maximum: {max_duration}s"
        
        # Check concurrent videos limit
        concurrent_limit = user_package.package.get_limit(PackageLimits.CONCURRENT_VIDEOS.value)
        if concurrent_limit > 0:
            # Import here to avoid circular import
            from videos.models import Video
            current_processing = Video.objects.filter(
                user=user,
                status__in=['in_queue', 'in_progress']
            ).count()
            
            if current_processing >= concurrent_limit:
                return False, f"Too many videos processing. Limit: {concurrent_limit}"
        
        return True, "OK"
    
    @staticmethod
    def record_video_creation(user_package, video_id, duration_seconds):
        """Record video creation usage"""
        return UsageService.record_usage(
            user_package=user_package,
            usage_type=PackageLimits.VIDEO_SECONDS.value,
            amount=duration_seconds,
            resource_id=str(video_id),
            metadata={
                'video_id': video_id,
                'duration': duration_seconds,
                'action': 'video_created'
            }
        )

    
    @staticmethod
    def get_usage_summary(user_package):
        """Get usage summary for current billing period"""
        period_start = user_package.get_current_billing_period_start()
        
        # Get all limit types from package
        limits = user_package.package.features.get('limits', {})
        summary = {}
        
        for usage_type, limit in limits.items():
            used = user_package.get_usage_for_period(usage_type, period_start)
            remaining = max(0, limit - used)
            
            summary[usage_type] = {
                'used': used,
                'limit': limit,
                'remaining': remaining,
                'percentage': (used / limit * 100) if limit > 0 else 0
            }
        
        return summary

    @staticmethod
    def get_feature_usage_stats(user_package, usage_type, days=30):
        """Get usage statistics for a specific feature over time"""
        from datetime import timedelta
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        usage_records = UsageTracking.objects.filter(
            user_package=user_package,
            usage_type=usage_type,
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values_list('amount', 'created_at')
        
        return list(usage_records)

# TODO: Check if still needed    
    # @staticmethod
    # def refund_usage(user_package, usage_type, amount, reason="refund"):
    #     """Refund usage (record negative usage)"""
    #     return UsageService.record_usage(
    #         user_package=user_package,
    #         usage_type=usage_type,
    #         amount=-amount,  # Negative amount for refund
    #         metadata={
    #             'action': 'refund',
    #             'reason': reason
    #         }
    #     )

    
    @staticmethod
    def reset_usage_for_billing_cycle(user_package):
        """
        Reset usage counters for new billing period
        Note: Our current system calculates usage dynamically based on billing period,
        but this method can be used for any special reset logic
        """
        from django.utils import timezone
        
        # Mark the start of new billing cycle
        user_package.started_at = timezone.now()
        user_package.save()
        
        # Log the reset
        UsageService.record_usage(
            user_package=user_package,
            usage_type='billing_cycle_reset',
            amount=1,
            metadata={
                'action': 'billing_cycle_reset',
                'reset_date': timezone.now().isoformat()
            }
        )
        
        return True
    
    @staticmethod
    def get_usage_analytics(user_package, usage_type, period_days=30):
        """
        Get detailed usage analytics
        
        Returns:
            dict: Analytics data including trends, patterns, etc.
        """
        from datetime import timedelta
        from django.utils import timezone
        from django.db.models import Sum, Count, Avg
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=period_days)
        
        # Get usage records for the period
        usage_records = user_package.usage_records.filter(
            usage_type=usage_type,
            created_at__gte=start_date,
            created_at__lte=end_date
        )
        
        # Calculate analytics
        total_usage = usage_records.aggregate(Sum('amount'))['amount__sum'] or 0
        usage_count = usage_records.count()
        avg_usage = usage_records.aggregate(Avg('amount'))['amount__avg'] or 0
        
        # Daily breakdown
        daily_usage = []
        for i in range(period_days):
            day_start = start_date + timedelta(days=i)
            day_end = day_start + timedelta(days=1)
            day_usage = usage_records.filter(
                created_at__gte=day_start,
                created_at__lt=day_end
            ).aggregate(Sum('amount'))['amount__sum'] or 0
            
            daily_usage.append({
                'date': day_start.date().isoformat(),
                'usage': day_usage
            })
        
        current_limit = user_package.package.get_limit(usage_type)
        current_period_usage = user_package.get_usage_for_period(usage_type)
        
        return {
            'usage_type': usage_type,
            'period_days': period_days,
            'total_usage': total_usage,
            'usage_count': usage_count,
            'average_usage': avg_usage,
            'daily_breakdown': daily_usage,
            'current_period': {
                'usage': current_period_usage,
                'limit': current_limit,
                'percentage': (current_period_usage / current_limit * 100) if current_limit > 0 else 0
            },
            'trends': {
                'peak_day': max(daily_usage, key=lambda x: x['usage']) if daily_usage else None,
                'average_daily': total_usage / period_days if period_days > 0 else 0
            }
        }
    
    @staticmethod
    def predict_usage_exhaustion(user_package, usage_type):
        """
        Predict when user will exhaust their usage limits
        
        Returns:
            dict: Prediction information
        """
        from datetime import timedelta
        from django.utils import timezone
        
        # Get usage trend from last 7 days
        usage_stats = UsageService.get_feature_usage_stats(user_package, usage_type, days=7)
        
        if not usage_stats:
            return {'prediction': 'insufficient_data'}
        
        # Calculate daily average usage
        daily_usage = sum(record[0] for record in usage_stats) / len(usage_stats)
        
        # Get remaining limit
        remaining = user_package.get_remaining_limit(usage_type)
        
        if daily_usage <= 0:
            return {'prediction': 'no_usage_trend'}
        
        # Calculate days until exhaustion
        days_until_exhaustion = remaining / daily_usage
        # Ensure days_until_exhaustion is an integer for timedelta
        days_until_exhaustion_int = int(days_until_exhaustion)
        exhaustion_date = timezone.now() + timedelta(days=days_until_exhaustion_int)
        
        # Get billing cycle end
        billing_period_start = user_package.get_current_billing_period_start()
        if user_package.package.billing_cycle == 'monthly':
            billing_period_end = billing_period_start + timedelta(days=30)
        else:
            billing_period_end = billing_period_start + timedelta(days=365)
        
        will_exhaust_before_reset = exhaustion_date < billing_period_end
        
        return {
            'prediction': 'calculated',
            'daily_average_usage': daily_usage,
            'remaining_limit': remaining,
            'days_until_exhaustion': days_until_exhaustion,
            'exhaustion_date': exhaustion_date.isoformat(),
            'will_exhaust_before_cycle_end': will_exhaust_before_reset,
            'billing_cycle_end': billing_period_end.isoformat(),
            'recommendation': 'upgrade_soon' if will_exhaust_before_reset else 'usage_sustainable'
        }