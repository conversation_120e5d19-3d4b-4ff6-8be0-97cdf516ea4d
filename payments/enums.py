from enum import Enum


class PackageLimits(Enum):
    """Enum for package limit types"""
    VIDEO_SECONDS = "video_seconds"
    MAX_SINGLE_VIDEO_SECONDS = "max_single_video_seconds"
    CONCURRENT_VIDEOS = "concurrent_videos"
    
    @classmethod
    def choices(cls):
        """Return choices for Django model fields"""
        return [(tag.value, tag.value) for tag in cls]
    
    @classmethod
    def get_all_values(cls):
        """Return all enum values as a list"""
        return [tag.value for tag in cls]
    
    def __str__(self):
        return self.value


class PackageFeatures(Enum):
    """Enum for package feature types"""
    PREMIUM_MEDIA_PROVIDERS = "premium_media_providers"
    YOUR_BRAND_LOGO_WATERMARK = "your_brand_logo_watermark"
    UNLIMITED_ACCOUNTS = "unlimited_accounts"
    TEAM_COLLABORATION = "team_collaboration"
    CUSTOMIZATION = "customization"
    
    @classmethod
    def choices(cls):
        """Return choices for Django model fields"""
        return [(tag.value, tag.value) for tag in cls]
    
    @classmethod
    def get_all_values(cls):
        """Return all enum values as a list"""
        return [tag.value for tag in cls]
    
    def __str__(self):
        return self.value