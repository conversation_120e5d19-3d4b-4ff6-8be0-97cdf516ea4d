from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from videos.models import Video, VideoTask
from accounts.models import Account
from .models import UserPackage, UsageTracking
from .services import UsageService
from .exceptions import raise_usage_limit_exceeded, raise_no_subscription
from .enums import PackageLimits, PackageFeatures
import logging

logger = logging.getLogger(__name__)


@receiver(pre_save, sender=Video)
def validate_video_usage_before_save(sender, instance, **kwargs):
    """
    Validate usage limits before saving video
    """
    if instance.pk is None:  # Only for new videos
        if hasattr(instance, 'user') and instance.user:
            try:
                # Check if user can create video
                duration = getattr(instance, 'duration', 30)
                can_create, message = UsageService.can_create_video(instance.user, duration)
                
                if not can_create:
                    user_package = instance.user.user_packages.filter(status='active').first()
                    if not user_package:
                        raise_no_subscription()
                    else:
                        raise_usage_limit_exceeded(user_package, PackageLimits.VIDEO_SECONDS.value, duration)
                        
            except Exception as e:
                logger.error(f"Error validating video usage: {str(e)}")
                raise


@receiver(post_save, sender=Video)
def record_video_usage_after_save(sender, instance, created, **kwargs):
    """
    Record usage after video is successfully created
    """
    if created and hasattr(instance, 'user') and instance.user:
        try:
            user_package = instance.user.user_packages.filter(status='active').first()
            if user_package:
                duration = getattr(instance, 'duration', 30)
                UsageService.record_video_creation(user_package, instance.id, duration)
                logger.info(f"Recorded video usage: {duration}s for user {instance.user.id}")
                
        except Exception as e:
            logger.error(f"Error recording video usage: {str(e)}")


@receiver(post_save, sender=UserPackage)
def handle_package_activation(sender, instance, created, **kwargs):
    """
    Handle package activation and deactivation
    """
    if instance.status == 'active':
        # Deactivate other packages for the same user
        UserPackage.objects.filter(
            user=instance.user,
            status='active'
        ).exclude(id=instance.id).update(status='cancelled')
        
        # Record package activation
        try:
            UsageService.record_usage(
                user_package=instance,
                usage_type='package_activation',
                amount=1,
                metadata={
                    'package_name': instance.package.name,
                    'action': 'package_activated'
                }
            )
            logger.info(f"Package {instance.package.name} activated for user {instance.user.id}")
            
        except Exception as e:
            logger.error(f"Error recording package activation: {str(e)}")


@receiver(post_save, sender=UsageTracking)
def check_usage_thresholds(sender, instance, created, **kwargs):
    """
    Check if usage is approaching limits and send alerts
    """
    if created:
        try:
            user_package = instance.user_package
            usage_type = instance.usage_type
            
            # Skip certain usage types
            if usage_type in ['package_activation', 'billing_cycle_reset']:
                return
            
            # Get current usage percentage
            current_usage = user_package.get_usage_for_period(usage_type)
            limit = user_package.package.get_limit(usage_type)
            
            if limit > 0:
                percentage = (current_usage / limit) * 100
                
                # Check thresholds
                if percentage >= 100:
                    # Usage exceeded
                    logger.warning(f"Usage exceeded for user {user_package.user.id}: {usage_type} at {percentage:.1f}%")
                    
                elif percentage >= 90:
                    # Critical warning (90%)
                    logger.warning(f"Critical usage warning for user {user_package.user.id}: {usage_type} at {percentage:.1f}%")
                    
                elif percentage >= 80:
                    # Warning (80%)
                    logger.info(f"Usage warning for user {user_package.user.id}: {usage_type} at {percentage:.1f}%")
                    
                # Here you could add email notifications, webhooks, etc.
                
        except Exception as e:
            logger.error(f"Error checking usage thresholds: {str(e)}")

# TODO: Check if still needed
# Utility function to manually trigger usage validation
# def validate_usage_manually(user, usage_type, amount):
#     """
#     Manually validate usage for any operation
#     """
#     user_package = user.user_packages.filter(status='active').first()
#     if not user_package:
#         raise_no_subscription()
    
#     if not user_package.can_use_feature(usage_type, amount):
#         raise_usage_limit_exceeded(user_package, usage_type, amount)
    
#     return True


# # Utility function to reset billing cycles
# def reset_billing_cycles():
#     """
#     Reset billing cycles for all active packages (for cron jobs)
#     """
#     from datetime import timedelta
    
#     today = timezone.now().date()
    
#     # Find packages that need billing cycle reset
#     monthly_packages = UserPackage.objects.filter(
#         status='active',
#         package__billing_cycle='monthly'
#     )
    
#     for user_package in monthly_packages:
#         billing_start = user_package.get_current_billing_period_start().date()
#         days_since_start = (today - billing_start).days
        
#         if days_since_start >= 30:  # Monthly cycle completed
#             UsageService.reset_usage_for_billing_cycle(user_package)
#             logger.info(f"Reset billing cycle for user {user_package.user.id}")
    
#     # Similar logic for yearly packages
#     yearly_packages = UserPackage.objects.filter(
#         status='active',
#         package__billing_cycle='yearly'
#     )
    
#     for user_package in yearly_packages:
#         billing_start = user_package.get_current_billing_period_start().date()
#         days_since_start = (today - billing_start).days
        
#         if days_since_start >= 365:  # Yearly cycle completed
#             UsageService.reset_usage_for_billing_cycle(user_package)
#             logger.info(f"Reset yearly billing cycle for user {user_package.user.id}")\
