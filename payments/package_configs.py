from .enums import PackageLimits, PackageFeatures

# Free Tier Package Configuration
FREE_TIER = {
    "limits": {
        PackageLimits.VIDEO_SECONDS.value: 60,  # 1 minute total per month
        PackageLimits.MAX_SINGLE_VIDEO_SECONDS.value: 30,  # 30 seconds per video max
        PackageLimits.CONCURRENT_VIDEOS.value: 1,  # Only 1 video processing at a time
    },
    "features": {
        PackageFeatures.PREMIUM_MEDIA_PROVIDERS.value: {"enabled": False},
        PackageFeatures.YOUR_BRAND_LOGO_WATERMARK.value: {"enabled": False},
        PackageFeatures.UNLIMITED_ACCOUNTS.value: {"enabled": True},
        PackageFeatures.TEAM_COLLABORATION.value: {"enabled": False},
        PackageFeatures.CUSTOMIZATION.value: {"enabled": False},
    }
}

# Starter Plan - $19/month
STARTER_PLAN = {
    "limits": {
        PackageLimits.VIDEO_SECONDS.value: 600,  # 10 minutes total per month
        PackageLimits.MAX_SINGLE_VIDEO_SECONDS.value: 60,  # 1 minute per video max
        PackageLimits.CONCURRENT_VIDEOS.value: 2
    },
    "features": {
        PackageFeatures.PREMIUM_MEDIA_PROVIDERS.value: {"enabled": False},
        PackageFeatures.YOUR_BRAND_LOGO_WATERMARK.value: {"enabled": True},
        PackageFeatures.UNLIMITED_ACCOUNTS.value: {"enabled": True},
        PackageFeatures.TEAM_COLLABORATION.value: {"enabled": False},
        PackageFeatures.CUSTOMIZATION.value: {"enabled": False},
    }
}

# Professional Plan - $49/month
PROFESSIONAL_PLAN = {
    "limits": {
        PackageLimits.VIDEO_SECONDS.value: 3000,  # 50 minutes total per month
        PackageLimits.MAX_SINGLE_VIDEO_SECONDS.value: 120,  # 2 minutes per video max
        PackageLimits.CONCURRENT_VIDEOS.value: 5
    },
    "features": {
        PackageFeatures.PREMIUM_MEDIA_PROVIDERS.value: {"enabled": True},
        PackageFeatures.YOUR_BRAND_LOGO_WATERMARK.value: {"enabled": True},
        PackageFeatures.UNLIMITED_ACCOUNTS.value: {"enabled": True},
        PackageFeatures.TEAM_COLLABORATION.value: {"enabled": False},
        PackageFeatures.CUSTOMIZATION.value: {"enabled": False},
    }
}

# Business Plan - $149/month
BUSINESS_PLAN = {
    "limits": {
        PackageLimits.VIDEO_SECONDS.value: 18000,  # 5 hours total per month
        PackageLimits.MAX_SINGLE_VIDEO_SECONDS.value: 300,  # 5 minutes per video max
        PackageLimits.CONCURRENT_VIDEOS.value: 10,
    },
    "features": {
        PackageFeatures.PREMIUM_MEDIA_PROVIDERS.value: {"enabled": True},
        PackageFeatures.YOUR_BRAND_LOGO_WATERMARK.value: {"enabled": True},
        PackageFeatures.UNLIMITED_ACCOUNTS.value: {"enabled": True},
        PackageFeatures.TEAM_COLLABORATION.value: {"enabled": False},
        PackageFeatures.CUSTOMIZATION.value: {"enabled": False},
    },
}

# Enterprise Plan - Custom pricing
ENTERPRISE_PLAN = {
    "limits": {
        PackageLimits.VIDEO_SECONDS.value: -1,  # Unlimited (-1 means no limit)
        PackageLimits.MAX_SINGLE_VIDEO_SECONDS.value: -1,  # Unlimited
        PackageLimits.CONCURRENT_VIDEOS.value: 25
    },
    "features": {
        PackageFeatures.PREMIUM_MEDIA_PROVIDERS.value: {"enabled": True},
        PackageFeatures.YOUR_BRAND_LOGO_WATERMARK.value: {"enabled": True},
        PackageFeatures.UNLIMITED_ACCOUNTS.value: {"enabled": True},
        PackageFeatures.TEAM_COLLABORATION.value: {"enabled": True},
        PackageFeatures.CUSTOMIZATION.value: {"enabled": True},
    }
}


# Utility functions for working with package configurations
def get_package_limit(package_config: dict, limit_type: PackageLimits) -> int:
    """Get a specific limit from package configuration using enum"""
    return package_config.get('limits', {}).get(limit_type.value, 0)


def get_package_feature(package_config: dict, feature_type: PackageFeatures) -> dict:
    """Get a specific feature configuration using enum"""
    return package_config.get('features', {}).get(feature_type.value, {})


def is_feature_enabled(package_config: dict, feature_type: PackageFeatures) -> bool:
    """Check if a specific feature is enabled using enum"""
    feature_config = get_package_feature(package_config, feature_type)
    return feature_config.get('enabled', False)


def get_all_package_limits() -> list:
    """Get all available package limit types"""
    return [limit_type.value for limit_type in PackageLimits]


def get_all_package_features() -> list:
    """Get all available package feature types"""
    return [feature_type.value for feature_type in PackageFeatures]


def validate_package_config(package_config: dict) -> dict:
    """Validate package configuration structure"""
    errors = []
    warnings = []
    
    # Check if limits section exists
    if 'limits' not in package_config:
        errors.append("Missing 'limits' section")
    else:
        limits = package_config['limits']
        # Check for required limits
        required_limits = [PackageLimits.VIDEO_SECONDS, PackageLimits.MAX_SINGLE_VIDEO_SECONDS, PackageLimits.CONCURRENT_VIDEOS]
        for limit_type in required_limits:
            if limit_type.value not in limits:
                warnings.append(f"Missing limit: {limit_type.value}")
    
    # Check if features section exists
    if 'features' not in package_config:
        errors.append("Missing 'features' section")
    else:
        features = package_config['features']
        # Check for required features
        required_features = [PackageFeatures.PREMIUM_MEDIA_PROVIDERS, PackageFeatures.YOUR_BRAND_LOGO_WATERMARK]
        for feature_type in required_features:
            if feature_type.value not in features:
                warnings.append(f"Missing feature: {feature_type.value}")
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings
    }
