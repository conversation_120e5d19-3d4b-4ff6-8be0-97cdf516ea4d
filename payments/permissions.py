from rest_framework.permissions import BasePermission
from payments.services import UsageService
from payments.exceptions import raise_no_subscription  # Add this import


class HasActivePackage(BasePermission):
    """
    Permission class to check if user has an active package
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        user_package = request.user.user_packages.filter(status='active').first()
        if user_package is None:
            raise_no_subscription()  # Raise the exception instead of returning False
        
        return True

class CanCreateVideo(BasePermission):
    """
    Permission class specifically for video creation
    """
    
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        
        # Get duration from request data
        duration = request.data.get('duration', 30) if hasattr(request, 'data') else 30
        
        can_create, message = UsageService.can_create_video(request.user, duration)
        if not can_create:
            # Store error message for use in view
            setattr(request, '_usage_error', message)
        
        return can_create
