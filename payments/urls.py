from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from .views import (
    PackageViewSet, UserPackageViewSet, PaymentHistoryViewSet,
    UsageAnalyticsViewSet, UpgradeRecommendationView,
    # Enhanced views
    PaymentSessionViewSet, PaymentGatewayInfoView,
    PaymentWebhookView, PaymentMethodsView,
    PaymentAnalyticsView, SubscriptionManagementView
)

router = DefaultRouter(trailing_slash=False)
router.register(r'packages', PackageViewSet, basename='package')
router.register(r'user-packages', UserPackageViewSet, basename='user-package')
router.register(r'payment-history', PaymentHistoryViewSet, basename='payment-history')
router.register(r'usage-analytics', UsageAnalyticsViewSet, basename='usage-analytics')
router.register(r'payment-sessions', PaymentSessionViewSet, basename='payment-session')

urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
    
    # Payment endpoints
    path('gateway-info', PaymentGatewayInfoView.as_view(), name='gateway-info'),
    path('payment-methods', PaymentMethodsView.as_view(), name='payment-methods'),
    path('analytics', PaymentAnalyticsView.as_view(), name='payment-analytics'),
    path('subscription', SubscriptionManagementView.as_view(), name='subscription-management'),
    path('upgrade-recommendations', UpgradeRecommendationView.as_view(), name='upgrade-recommendations'),
    
    # Webhook endpoints
    path('webhook/<str:gateway_name>', PaymentWebhookView.as_view(), name='payment-webhook'),
    path('webhook/razorpay', PaymentWebhookView.as_view(), {'gateway_name': 'razorpay'}, name='razorpay-webhook'),
]