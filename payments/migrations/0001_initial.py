# Generated by Django 5.2.1 on 2025-09-04 07:45

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Package',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('billing_cycle', models.CharField(choices=[('monthly', 'Monthly'), ('yearly', 'Yearly'), ('one_time', 'One Time')], max_length=20)),
                ('features', models.JSONField(default=dict)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'packages',
            },
        ),
        migrations.CreateModel(
            name='PaymentSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(db_index=True, max_length=100, unique=True)),
                ('gateway_name', models.CharField(max_length=50)),
                ('gateway_session_id', models.CharField(blank=True, max_length=100, null=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=10)),
                ('status', models.CharField(choices=[('created', 'Created'), ('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('expired', 'Expired'), ('cancelled', 'Cancelled')], default='created', max_length=20)),
                ('payment_method', models.CharField(blank=True, max_length=100, null=True)),
                ('gateway_metadata', models.JSONField(default=dict)),
                ('is_test_payment', models.BooleanField(default=False)),
                ('expires_at', models.DateTimeField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_sessions', to='payments.package')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'payment_sessions',
            },
        ),
        migrations.CreateModel(
            name='UserPackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('active', 'Active'), ('cancelled', 'Cancelled'), ('expired', 'Expired')], default='active', max_length=20)),
                ('started_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_at', models.DateTimeField(blank=True, null=True)),
                ('cancelled_at', models.DateTimeField(blank=True, null=True)),
                ('is_trial', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_packages', to='payments.package')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_packages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'user_packages',
            },
        ),
        migrations.CreateModel(
            name='PaymentHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('succeeded', 'Succeeded'), ('failed', 'Failed'), ('refunded', 'Refunded'), ('partially_refunded', 'Partially Refunded')], max_length=20)),
                ('payment_gateway', models.CharField(choices=[('razorpay', 'Razorpay')], default='razorpay', max_length=50)),
                ('gateway_transaction_id', models.CharField(blank=True, max_length=100, null=True)),
                ('gateway_metadata', models.JSONField(default=dict)),
                ('payment_status_details', models.JSONField(default=dict)),
                ('payment_method', models.CharField(default='card', max_length=100)),
                ('is_test_payment', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_history', to='payments.package')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_history', to=settings.AUTH_USER_MODEL)),
                ('payment_session', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='payments.paymentsession')),
                ('user_package', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_history', to='payments.userpackage')),
            ],
            options={
                'db_table': 'payment_history',
            },
        ),
        migrations.CreateModel(
            name='UsageTracking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('usage_type', models.CharField(db_index=True, max_length=50)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('resource_id', models.CharField(blank=True, max_length=100, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ('user_package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usage_records', to='payments.userpackage')),
            ],
            options={
                'db_table': 'usage_tracking',
                'indexes': [models.Index(fields=['user_package', 'usage_type', 'created_at'], name='usage_track_user_pa_dcb5e0_idx'), models.Index(fields=['usage_type', 'created_at'], name='usage_track_usage_t_ca2fdf_idx')],
            },
        ),
    ]
