from django.db import models
from django.utils import timezone
from datetime import datetime
from authentication.models import User


class Package(models.Model):
    class Meta:
        db_table = 'packages'

    BILLING_CYCLE_CHOICES = (
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly'),
        ('one_time', 'One Time'),
    )
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    billing_cycle = models.CharField(max_length=20, choices=BILLING_CYCLE_CHOICES)
    features = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    def __str__(self):
        return f"{self.name} - ${self.price} ({self.billing_cycle})"

    def get_limit(self, limit_type):
        """Get limit for any feature type"""
        return self.features.get('limits', {}).get(limit_type, 0)
    
    def get_feature_config(self, feature_name):
        """Get configuration for any feature"""
        return self.features.get(feature_name, {})


class UserPackage(models.Model):
    class Meta:
        db_table = 'user_packages'

    STATUS_CHOICES = (
        ('active', 'Active'),
        ('cancelled', 'Cancelled'),
        ('expired', 'Expired'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_packages')
    package = models.ForeignKey(Package, on_delete=models.CASCADE, related_name='user_packages')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    started_at = models.DateTimeField(default=timezone.now)
    end_at = models.DateTimeField(blank=True, null=True)
    cancelled_at = models.DateTimeField(blank=True, null=True)
    is_trial = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)
    
    def __str__(self):
        return f"{self.user.email} - {self.package.name} - {self.status}"
    
    def calculate_end_date(self):
        """Calculate end date based on billing cycle and start date"""
        from datetime import timedelta
        import calendar
        
        if not self.started_at:
            return None
            
        if self.package.billing_cycle == 'monthly':
            # Add approximately 30 days for monthly (can be refined later)
            return self.started_at + timedelta(days=30)
        elif self.package.billing_cycle == 'yearly':
            # Add 365 days for yearly (can be refined later)
            return self.started_at + timedelta(days=365)
        elif self.package.billing_cycle == 'one_time':
            # For one-time packages, no end date (lifetime)
            return None
        else:
            # Default to monthly if billing cycle is unknown
            return self.started_at + timedelta(days=30)
    
    def save(self, *args, **kwargs):
        """Override save to automatically set end_at if not provided"""
        # Only calculate end_at if it's not already set and package has recurring billing
        if not self.end_at and self.package and self.started_at:
            if self.package.billing_cycle in ['monthly', 'yearly']:
                self.end_at = self.calculate_end_date()
        super().save(*args, **kwargs)
    
    def get_current_billing_period_start(self):
        """Calculate current billing period start based on billing cycle"""
        if self.package.billing_cycle == 'monthly':
            today = timezone.now().date()
            start_day = self.started_at.day
            if today.day >= start_day:
                return datetime(today.year, today.month, start_day).replace(tzinfo=timezone.get_current_timezone())
            else:
                # Previous month
                if today.month == 1:
                    prev_month = 12
                    year = today.year - 1
                else:
                    prev_month = today.month - 1
                    year = today.year
                return datetime(year, prev_month, start_day).replace(tzinfo=timezone.get_current_timezone())
        elif self.package.billing_cycle == 'yearly':
            today = timezone.now().date()
            start_date = self.started_at.date()
            current_year_start = start_date.replace(year=today.year)
            if today >= current_year_start:
                return datetime.combine(current_year_start, datetime.min.time()).replace(tzinfo=timezone.get_current_timezone())
            else:
                prev_year_start = start_date.replace(year=today.year - 1)
                return datetime.combine(prev_year_start, datetime.min.time()).replace(tzinfo=timezone.get_current_timezone())
        else:
            return self.started_at
    
    def get_usage_for_period(self, usage_type, period_start=None):
        """Get usage for any metric in current billing period"""
        if period_start is None:
            period_start = self.get_current_billing_period_start()
        
        return UsageTracking.objects.filter(
            user_package=self,
            usage_type=usage_type,
            created_at__gte=period_start
        ).aggregate(
            total=models.Sum('amount')
        )['total'] or 0
    
    def get_remaining_limit(self, limit_type):
        """Get remaining limit for any feature"""
        total_limit = self.package.get_limit(limit_type)
        used = self.get_usage_for_period(limit_type)
        return max(0, total_limit - used)
    
    def can_use_feature(self, feature_type, amount=1):
        """Check if user can use a feature"""
        remaining = self.get_remaining_limit(feature_type)
        return remaining >= amount
    
    def has_feature_access(self, feature_name):
        """Check if user has access to a specific feature"""
        feature_config = self.package.get_feature_config(feature_name)
        return feature_config.get('enabled', False)


class UsageTracking(models.Model):
    """Dynamic usage tracking for any type of usage"""
    class Meta:
        db_table = 'usage_tracking'
        indexes = [
            models.Index(fields=['user_package', 'usage_type', 'created_at']),
            models.Index(fields=['usage_type', 'created_at']),
        ]

    user_package = models.ForeignKey(UserPackage, on_delete=models.CASCADE, related_name='usage_records')
    usage_type = models.CharField(max_length=50, db_index=True)  # 'video_seconds', etc.
    amount = models.DecimalField(max_digits=15, decimal_places=2)  # Amount used
    resource_id = models.CharField(max_length=100, blank=True, null=True)  # Reference to related object (video_id, etc.)
    metadata = models.JSONField(default=dict, blank=True)  # Additional context data
    created_at = models.DateTimeField(default=timezone.now, db_index=True)
    
    def __str__(self):
        return f"{self.user_package.user.email} - {self.usage_type}: {self.amount}"

class PaymentSession(models.Model):
    """Track payment sessions across different gateways"""
    class Meta:
        db_table = 'payment_sessions'

    STATUS_CHOICES = (
        ('created', 'Created'),
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled'),
    )
    
    session_id = models.CharField(max_length=100, unique=True, db_index=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payment_sessions')
    package = models.ForeignKey(Package, on_delete=models.CASCADE, related_name='payment_sessions')
    gateway_name = models.CharField(max_length=50)
    gateway_session_id = models.CharField(max_length=100, blank=True, null=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=10, default='USD')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='created')
    payment_method = models.CharField(max_length=100, blank=True, null=True)
    gateway_metadata = models.JSONField(default=dict)
    is_test_payment = models.BooleanField(default=False)
    expires_at = models.DateTimeField()
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.session_id} - {self.gateway_name} - {self.status}"

class PaymentHistory(models.Model):
    class Meta:
        db_table = 'payment_history'

    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('succeeded', 'Succeeded'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
        ('partially_refunded', 'Partially Refunded'),
    )
    
    GATEWAY_CHOICES = (
        ('razorpay', 'Razorpay'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payment_history')
    package = models.ForeignKey(Package, on_delete=models.CASCADE, related_name='payment_history')
    user_package = models.ForeignKey(UserPackage, on_delete=models.SET_NULL, related_name='payment_history', null=True)
    payment_session = models.ForeignKey(PaymentSession, on_delete=models.SET_NULL, null=True, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=10, default='USD')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    payment_gateway = models.CharField(max_length=50, choices=GATEWAY_CHOICES, default='razorpay')
    gateway_transaction_id = models.CharField(max_length=100, blank=True, null=True)
    gateway_metadata = models.JSONField(default=dict)
    payment_status_details = models.JSONField(default=dict)
    payment_method = models.CharField(max_length=100, default='card')
    is_test_payment = models.BooleanField(default=False)    
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.user.email} - {self.package.name} - {self.amount} - {self.status}"
