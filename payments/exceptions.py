from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status


class UsageLimitExceededException(Exception):
    """Exception raised when usage limit is exceeded"""
    
    def __init__(self, usage_type, used, limit, message=None):
        self.usage_type = usage_type
        self.used = used
        self.limit = limit
        self.message = message or f"Usage limit exceeded for {usage_type}: {used}/{limit}"
        super().__init__(self.message)


class FeatureNotAvailableException(Exception):
    """Exception raised when feature is not available in user's plan"""
    
    def __init__(self, feature_name, current_plan, message=None):
        self.feature_name = feature_name
        self.current_plan = current_plan
        self.message = message or f"Feature '{feature_name}' not available in {current_plan} plan"
        super().__init__(self.message)


class UpgradeRequiredException(Exception):
    """Exception raised when an upgrade is required to perform action"""
    
    def __init__(self, action, current_plan, suggested_plans=None, message=None):
        self.action = action
        self.current_plan = current_plan
        self.suggested_plans = suggested_plans or []
        self.message = message or f"Upgrade required to perform: {action}"
        super().__init__(self.message)


class NoActiveSubscriptionException(Exception):
    """Exception raised when user has no active subscription"""
    
    def __init__(self, message=None):
        self.message = message or "No active subscription found"
        super().__init__(self.message)


def custom_exception_handler(exc, context):
    """
    Custom exception handler for usage-related exceptions
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    if response is not None:
        return response
    
    # Handle our custom exceptions
    if isinstance(exc, UsageLimitExceededException):
        custom_response_data = {
            'error': exc.message,
            'error_code': 'USAGE_LIMIT_EXCEEDED',
            'usage_type': exc.usage_type,
            'used': exc.used,
            'limit': exc.limit,
            'upgrade_required': True
        }
        return Response(custom_response_data, status=status.HTTP_429_TOO_MANY_REQUESTS)
    
    elif isinstance(exc, FeatureNotAvailableException):
        custom_response_data = {
            'error': exc.message,
            'error_code': 'FEATURE_NOT_AVAILABLE',
            'feature_name': exc.feature_name,
            'current_plan': exc.current_plan,
            'upgrade_required': True
        }
        return Response(custom_response_data, status=status.HTTP_403_FORBIDDEN)
    
    elif isinstance(exc, UpgradeRequiredException):
        custom_response_data = {
            'error': exc.message,
            'error_code': 'UPGRADE_REQUIRED',
            'action': exc.action,
            'current_plan': exc.current_plan,
            'suggested_plans': exc.suggested_plans,
            'upgrade_required': True
        }
        return Response(custom_response_data, status=status.HTTP_402_PAYMENT_REQUIRED)
    
    elif isinstance(exc, NoActiveSubscriptionException):
        custom_response_data = {
            'error': exc.message,
            'error_code': 'NO_ACTIVE_SUBSCRIPTION',
            'upgrade_required': True
        }
        return Response(custom_response_data, status=status.HTTP_403_FORBIDDEN)
    
    # Return None to let Django handle other exceptions
    return None


# Utility functions for raising exceptions with context
def raise_usage_limit_exceeded(user_package, usage_type, attempted_amount):
    """Helper function to raise usage limit exceeded exception"""
    used = user_package.get_usage_for_period(usage_type)
    limit = user_package.package.get_limit(usage_type)
    
    raise UsageLimitExceededException(
        usage_type=usage_type,
        used=used,
        limit=limit,
        message=f"Cannot use {attempted_amount} {usage_type}. Used: {used}, Limit: {limit}"
    )

# TODO: Check if still needed
# def raise_feature_not_available(user_package, feature_name):
#     """Helper function to raise feature not available exception"""
#     raise FeatureNotAvailableException(
#         feature_name=feature_name,
#         current_plan=user_package.package.name
#     )

def raise_no_subscription():
    """Helper function to raise no subscription exception"""
    raise NoActiveSubscriptionException()