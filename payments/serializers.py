from rest_framework import serializers
from .models import (
    Package, UserPackage, PaymentHistory, PaymentSession
)

class PackageSerializer(serializers.ModelSerializer):
    class Meta:
        model = Package
        fields = '__all__'
        read_only_fields = ('id', 'created_at')

class UserPackageSerializer(serializers.ModelSerializer):
    package_details = PackageSerializer(source='package', read_only=True)
    
    class Meta:
        model = UserPackage
        fields = '__all__'
        read_only_fields = ('id', 'user', 'created_at')

class PaymentHistorySerializer(serializers.ModelSerializer):
    package_name = serializers.CharField(source='package.name', read_only=True)
    
    class Meta:
        model = PaymentHistory
        fields = '__all__'
        read_only_fields = ('id', 'user', 'created_at')

class CreateCheckoutSessionSerializer(serializers.Serializer):
    package_id = serializers.IntegerField(required=True)
    success_url = serializers.URLField(required=True)
    cancel_url = serializers.URLField(required=True)
    
    def validate_package_id(self, value):
        try:
            package = Package.objects.get(id=value, is_active=True)
            return value
        except Package.DoesNotExist:
            raise serializers.ValidationError("Package does not exist or is not active")


# Enhanced Serializers

class PaymentSessionSerializer(serializers.ModelSerializer):
    """Serializer for PaymentSession model"""
    
    user_email = serializers.CharField(source='user.email', read_only=True)
    package_name = serializers.CharField(source='package.name', read_only=True)
    is_expired = serializers.SerializerMethodField()
    
    class Meta:
        model = PaymentSession
        fields = [
            'id', 'session_id', 'user_email', 'package_name',
            'gateway_name', 'gateway_session_id', 'amount', 'currency',
            'status', 'payment_method', 'gateway_metadata',
            'is_test_payment', 'is_expired', 'expires_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'session_id', 'created_at', 'updated_at']
    
    def get_is_expired(self, obj):
        """Check if payment session is expired"""
        from django.utils import timezone
        if hasattr(obj, 'expires_at') and obj.expires_at:
            return timezone.now() > obj.expires_at
        return False


class CreatePaymentSessionSerializer(serializers.Serializer):
    """Enhanced serializer for creating payment sessions"""
    
    package_id = serializers.IntegerField()
    gateway_name = serializers.CharField(required=False, allow_blank=True, default='auto')
    callback_url = serializers.URLField(required=False, allow_blank=True)
    metadata = serializers.JSONField(required=False, default=dict)
    payment_methods = serializers.ListField(
        child=serializers.CharField(), 
        required=False, 
        default=list
    )
    
    def validate_package_id(self, value):
        """Validate that package exists and is active"""
        try:
            package = Package.objects.get(id=value, is_active=True)
            return value
        except Package.DoesNotExist:
            raise serializers.ValidationError("Package not found or inactive")
    
    def validate_gateway_name(self, value):
        """Validate gateway name if provided"""
        if not value or value == 'auto':
            return 'auto'  # Will be auto-selected
        
        allowed_gateways = ['razorpay', 'stripe', 'auto']
        if value not in allowed_gateways:
            raise serializers.ValidationError(f"Invalid gateway name. Allowed: {allowed_gateways}")
        return value
    
    def validate_callback_url(self, value):
        """Validate callback URL format"""
        if value and not value.startswith(('http://', 'https://')):
            raise serializers.ValidationError("Callback URL must start with http:// or https://")
        return value
    
    def validate_metadata(self, value):
        """Validate metadata is a dictionary"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("Metadata must be a JSON object")
        return value
    
    def validate_gateway_name(self, value):
        """Validate gateway name if provided"""
        if value and value not in ['razorpay', 'stripe']:
            raise serializers.ValidationError("Invalid gateway name")
        return value or 'razorpay'

class EnhancedPaymentHistorySerializer(PaymentHistorySerializer):
    """Enhanced payment history serializer with additional fields"""
    
    gateway_display_name = serializers.SerializerMethodField()
    is_test_payment = serializers.BooleanField(default=False)
    
    class Meta(PaymentHistorySerializer.Meta):
        fields = '__all__'  # Use all fields from parent plus computed fields
    
    def get_gateway_display_name(self, obj):
        """Get human-readable gateway name"""
        gateway_names = {
            'razorpay': 'Razorpay',
            'stripe': 'Stripe'
        }
        return gateway_names.get(getattr(obj, 'payment_gateway', ''), 'Unknown')
