from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from payments.models import UserPackage
from payments.services import UsageService
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Reset usage tracking for users whose billing cycles have completed'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be reset without actually doing it',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force reset even if not at exact billing cycle end',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force_reset = options['force']
        
        self.stdout.write("🔄 Starting billing cycle reset process...")
        
        today = timezone.now()
        reset_count = 0
        
        # Process monthly packages
        monthly_packages = UserPackage.objects.filter(
            status='active',
            package__billing_cycle='monthly'
        )
        
        for user_package in monthly_packages:
            try:
                billing_start = user_package.get_current_billing_period_start()
                days_since_start = (today - billing_start).days
                
                should_reset = False
                if force_reset:
                    should_reset = days_since_start >= 25  # Allow some flexibility
                else:
                    should_reset = days_since_start >= 30
                
                if should_reset:
                    if dry_run:
                        self.stdout.write(
                            f"[DRY RUN] Would reset billing cycle for user {user_package.user.email} "
                            f"(started {days_since_start} days ago)"
                        )
                    else:
                        UsageService.reset_usage_for_billing_cycle(user_package)
                        self.stdout.write(
                            self.style.SUCCESS(
                                f"✓ Reset monthly billing cycle for user {user_package.user.email}"
                            )
                        )
                    reset_count += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"✗ Error processing user {user_package.user.email}: {str(e)}"
                    )
                )
                logger.error(f"Error resetting billing cycle for user {user_package.user.id}: {str(e)}")
        
        # Process yearly packages
        yearly_packages = UserPackage.objects.filter(
            status='active',
            package__billing_cycle='yearly'
        )
        
        for user_package in yearly_packages:
            try:
                billing_start = user_package.get_current_billing_period_start()
                days_since_start = (today - billing_start).days
                
                should_reset = False
                if force_reset:
                    should_reset = days_since_start >= 360  # Allow some flexibility
                else:
                    should_reset = days_since_start >= 365
                
                if should_reset:
                    if dry_run:
                        self.stdout.write(
                            f"[DRY RUN] Would reset billing cycle for user {user_package.user.email} "
                            f"(started {days_since_start} days ago)"
                        )
                    else:
                        UsageService.reset_usage_for_billing_cycle(user_package)
                        self.stdout.write(
                            self.style.SUCCESS(
                                f"✓ Reset yearly billing cycle for user {user_package.user.email}"
                            )
                        )
                    reset_count += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"✗ Error processing user {user_package.user.email}: {str(e)}"
                    )
                )
                logger.error(f"Error resetting yearly billing cycle for user {user_package.user.id}: {str(e)}")
        
        # Summary
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f"\n📊 DRY RUN SUMMARY: {reset_count} billing cycles would be reset")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f"\n🎉 COMPLETED: {reset_count} billing cycles have been reset")
            )
            
        if reset_count == 0:
            self.stdout.write("ℹ️  No billing cycles needed to be reset at this time")
        
        self.stdout.write("\n📝 TIP: Run with --dry-run to preview changes before applying them")
        self.stdout.write("📝 TIP: Run with --force to reset cycles that are close to completion")