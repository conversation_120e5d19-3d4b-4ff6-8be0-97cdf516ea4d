from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from payments.models import UsageTracking
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Clean up old usage tracking records to maintain database performance'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=365,
            help='Delete usage records older than this many days (default: 365)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually doing it',
        )
        parser.add_argument(
            '--keep-billing-resets',
            action='store_true',
            help='Keep billing cycle reset records even if they are old',
        )

    def handle(self, *args, **options):
        days_to_keep = options['days']
        dry_run = options['dry_run']
        keep_billing_resets = options['keep_billing_resets']
        
        cutoff_date = timezone.now() - timedelta(days=days_to_keep)
        
        self.stdout.write(f"🧹 Starting cleanup of usage records older than {days_to_keep} days...")
        self.stdout.write(f"📅 Cutoff date: {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Build query to find old records
        old_records_query = UsageTracking.objects.filter(
            created_at__lt=cutoff_date
        )
        
        # Optionally exclude billing reset records
        if keep_billing_resets:
            old_records_query = old_records_query.exclude(
                usage_type='billing_cycle_reset'
            )
        
        # Count records to be deleted
        total_old_records = old_records_query.count()
        
        if total_old_records == 0:
            self.stdout.write(self.style.SUCCESS("✅ No old usage records found to clean up"))
            return
        
        # Break down by usage type for better insight
        usage_type_breakdown = {}
        for record in old_records_query.values('usage_type').distinct():
            usage_type = record['usage_type']
            count = old_records_query.filter(usage_type=usage_type).count()
            usage_type_breakdown[usage_type] = count
        
        # Display what will be deleted
        self.stdout.write(f"\n📊 Found {total_old_records:,} old usage records:")
        for usage_type, count in sorted(usage_type_breakdown.items()):
            self.stdout.write(f"  • {usage_type}: {count:,} records")
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f"\n[DRY RUN] Would delete {total_old_records:,} usage records")
            )
            
            # Show some example records that would be deleted
            sample_records = old_records_query.order_by('created_at')[:5]
            if sample_records:
                self.stdout.write("\n📋 Sample records that would be deleted:")
                for record in sample_records:
                    self.stdout.write(
                        f"  • {record.created_at.strftime('%Y-%m-%d')} - "
                        f"{record.usage_type} - {record.amount} - "
                        f"User: {record.user_package.user.email}"
                    )
        else:
            # Confirm deletion
            self.stdout.write(
                self.style.WARNING(
                    f"\n⚠️  This will permanently delete {total_old_records:,} usage records"
                )
            )
            
            confirm = input("Are you sure you want to proceed? (yes/no): ")
            if confirm.lower() != 'yes':
                self.stdout.write("❌ Operation cancelled")
                return
            
            # Perform deletion in batches to avoid memory issues
            batch_size = 10000
            deleted_total = 0
            
            while True:
                # Get a batch of IDs to delete
                batch_ids = list(
                    old_records_query.values_list('id', flat=True)[:batch_size]
                )
                
                if not batch_ids:
                    break
                
                # Delete the batch
                deleted_count = UsageTracking.objects.filter(
                    id__in=batch_ids
                ).delete()[0]
                
                deleted_total += deleted_count
                
                self.stdout.write(
                    f"🗑️  Deleted batch: {deleted_count:,} records "
                    f"(Total: {deleted_total:,}/{total_old_records:,})"
                )
                
                # Log progress
                logger.info(f"Deleted usage tracking batch: {deleted_count} records")
            
            self.stdout.write(
                self.style.SUCCESS(f"\n🎉 Successfully deleted {deleted_total:,} old usage records")
            )
        
        # Show current database stats
        total_remaining = UsageTracking.objects.count()
        self.stdout.write(f"\n📈 Database stats:")
        self.stdout.write(f"  • Total usage records remaining: {total_remaining:,}")
        
        if not dry_run and total_old_records > 0:
            space_saved_estimate = total_old_records * 0.5  # Rough estimate: 0.5KB per record
            if space_saved_estimate > 1024:
                self.stdout.write(f"  • Estimated space saved: ~{space_saved_estimate/1024:.1f} MB")
            else:
                self.stdout.write(f"  • Estimated space saved: ~{space_saved_estimate:.0f} KB")
        
        # Usage recommendations
        self.stdout.write(f"\n💡 Recommendations:")
        self.stdout.write(f"  • Run this command monthly to maintain optimal performance")
        self.stdout.write(f"  • Consider keeping 12 months of data for annual analytics")
        self.stdout.write(f"  • Use --keep-billing-resets to preserve billing cycle records")
        
        if dry_run:
            self.stdout.write(f"  • Run without --dry-run to actually perform the cleanup")