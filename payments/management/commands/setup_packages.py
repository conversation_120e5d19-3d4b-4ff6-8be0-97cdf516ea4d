from django.core.management.base import BaseCommand
from payments.models import Package
from payments.package_configs import (
    FREE_TIER, STARTER_PLAN, PROFESSIONAL_PLAN, 
    BUSINESS_PLAN, ENTERPRISE_PLAN
)
from payments.enums import PackageLimits


class Command(BaseCommand):
    help = 'Create initial SaaS packages for AIVIA'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreate packages even if they exist',
        )

    def handle(self, *args, **options):
        packages_data = [
            {
                'name': 'Free Tier',
                'description': 'Get started with basic video creation features. Perfect for testing AIVIA.',
                'price': 0.00,
                'billing_cycle': 'monthly',
                'features': FREE_TIER,
            },
            {
                'name': 'Starter',
                'description': 'Ideal for content creators and small businesses. Remove watermarks and create professional videos.',
                'price': 19.00,
                'billing_cycle': 'monthly',
                'features': STARTER_PLAN,
            },
            {
                'name': 'Professional',
                'description': 'Perfect for growing businesses and agencies. Advanced features and team collaboration.',
                'price': 49.00,
                'billing_cycle': 'monthly',
                'features': PROFESSIONAL_PLAN,
            },
            {
                'name': 'Business',
                'description': 'For large teams and enterprises. White-label options and dedicated support.',
                'price': 149.00,
                'billing_cycle': 'monthly',
                'features': BUSINESS_PLAN,
            },
            {
                'name': 'Enterprise',
                'description': 'Custom solutions for enterprise needs. Unlimited everything with premium support.',
                'price': 999.00,  # Placeholder price
                'billing_cycle': 'monthly',
                'features': ENTERPRISE_PLAN,
            }
        ]

        for package_data in packages_data:
            package_name = package_data['name']
            
            if options['force']:
                # Delete existing package if force flag is used
                Package.objects.filter(name=package_name).delete()
                self.stdout.write(f"Deleted existing package: {package_name}")
            
            package, created = Package.objects.get_or_create(
                name=package_name,
                defaults=package_data
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f"✓ Created package: {package_name}")
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f"Package already exists: {package_name}")
                )
        
        self.stdout.write(
            self.style.SUCCESS("\n🎉 Package setup completed!")
        )
        
        # Display package summary
        self.stdout.write("\n📦 Available Packages:")
        for package in Package.objects.filter(is_active=True).order_by('price'):
            limits = package.features.get('limits', {})
            video_seconds = limits.get(PackageLimits.VIDEO_SECONDS.value, 0)
            video_minutes = video_seconds / 60 if video_seconds > 0 else "Unlimited"
            
            self.stdout.write(
                f"  • {package.name}: ${package.price}/month - "
                f"{video_minutes} minutes video creation"
            )