import razorpay
import hmac
import hashlib
import json
import logging
from typing import Dict, Any, List
from django.conf import settings
from django.utils import timezone

from .base import (
    PaymentGateway, PaymentRequest, PaymentResponse, PaymentVerificationResponse,
    RefundRequest, RefundResponse, PaymentStatus, PaymentMethod
)

logger = logging.getLogger(__name__)


class RazorpayGateway(PaymentGateway):
    """
    Razorpay payment gateway implementation
    Handles payments through Razorpay's Indian payment infrastructure
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.client = razorpay.Client(
            auth=(config['api_key'], config['api_secret'])
        )
        self.webhook_secret = config.get('webhook_secret', '')
    
    def get_gateway_name(self) -> str:
        return "razorpay"
    
    def get_supported_features(self) -> Dict[str, bool]:
        return {
            'subscriptions': True,
            'refunds': True,
            'partial_refunds': True,
            'webhooks': True,
            'upi': True,
            'cards': True,
            'net_banking': True,
            'wallets': True,
            'emi': True,
            'bank_transfer': False,
            'international_cards': True,
            'recurring_payments': True
        }
    
    def get_required_config_fields(self) -> List[str]:
        return ['api_key', 'api_secret']
    
    def create_payment_session(self, request: PaymentRequest) -> PaymentResponse:
        """Create Razorpay order"""
        
        try:
            # Convert amount to paise (Razorpay expects amount in smallest currency unit)
            amount_paise = self.format_amount(request.amount, request.currency)
            
            order_data = {
                'amount': amount_paise,
                'currency': request.currency.upper(),
                'receipt': f"receipt_{request.user_id}_{request.package_id}",
                'notes': {
                    'user_id': request.user_id,
                    'package_id': request.package_id,
                    'description': request.description
                }
            }
            
            # Add metadata
            if request.metadata:
                order_data['notes'].update(request.metadata)
            
            # Create order
            order = self.client.order.create(data=order_data)
            
            session_id = self.generate_session_id()
            
            return PaymentResponse(
                success=True,
                session_id=session_id,
                gateway_session_id=order['id'],
                payment_url="",  # Razorpay uses frontend integration
                status=PaymentStatus.CREATED,
                gateway_response={
                    'order_id': order['id'],
                    'amount': order['amount'],
                    'currency': order['currency'],
                    'key': self.config['api_key'],
                    'razorpay_order': order
                }
            )
            
        except Exception as e:
            return PaymentResponse(
                success=False,
                session_id="",
                gateway_session_id="",
                status=PaymentStatus.FAILED,
                error_message=str(e),
                error_code="RAZORPAY_ORDER_CREATION_FAILED"
            )
    
    def verify_payment(self, session_id: str, gateway_data: Dict[str, Any] = None) -> PaymentVerificationResponse:
        """Verify Razorpay payment"""
        
        try:
            logger.info(f"Starting Razorpay payment verification for session: {session_id}")
            logger.debug(f"Gateway data received: {gateway_data}")
            
            if not gateway_data:
                logger.error("No gateway data provided for verification")
                return PaymentVerificationResponse(
                    success=False,
                    payment_id=session_id,
                    status=PaymentStatus.FAILED,
                    amount=0,
                    currency="USD",
                    payment_method="unknown",
                    error_message="No gateway data provided for verification"
                )
            
            # Get payment details from gateway_data
            razorpay_payment_id = gateway_data.get('razorpay_payment_id')
            razorpay_order_id = gateway_data.get('razorpay_order_id')
            razorpay_signature = gateway_data.get('razorpay_signature')
            
            logger.info(f"Razorpay verification data - Payment ID: {razorpay_payment_id}, Order ID: {razorpay_order_id}, Signature: {razorpay_signature[:10]}..." if razorpay_signature else "No signature")
            
            if not all([razorpay_payment_id, razorpay_order_id, razorpay_signature]):
                missing_fields = []
                if not razorpay_payment_id:
                    missing_fields.append('razorpay_payment_id')
                if not razorpay_order_id:
                    missing_fields.append('razorpay_order_id') 
                if not razorpay_signature:
                    missing_fields.append('razorpay_signature')
                
                error_msg = f"Missing required Razorpay verification parameters: {', '.join(missing_fields)}"
                logger.error(error_msg)
                return PaymentVerificationResponse(
                    success=False,
                    payment_id=session_id,
                    status=PaymentStatus.FAILED,
                    amount=0,
                    currency="USD",
                    payment_method="unknown",
                    error_message=error_msg
                )
            
            # Verify signature
            logger.info(f"Verifying signature for order: {razorpay_order_id}, payment: {razorpay_payment_id}")
            signature_valid = self._verify_razorpay_signature(
                razorpay_order_id, razorpay_payment_id, razorpay_signature
            )
            logger.info(f"Signature verification result: {signature_valid}")
            
            if not signature_valid:
                logger.error("Payment signature verification failed")
                return PaymentVerificationResponse(
                    success=False,
                    payment_id=session_id,
                    status=PaymentStatus.FAILED,
                    amount=0,
                    currency="USD",
                    payment_method="unknown",
                    error_message="Invalid payment signature"
                )
            
            # Fetch payment details from Razorpay
            logger.info(f"Fetching payment details from Razorpay for payment ID: {razorpay_payment_id}")
            payment = self.client.payment.fetch(razorpay_payment_id)
            logger.info(f"Razorpay payment status: {payment.get('status')}")
            logger.debug(f"Razorpay payment details: {payment}")
            
            # Map Razorpay status to our status
            status = self._map_razorpay_status(payment['status'])
            logger.info(f"Mapped payment status: {status}")
            
            verification_success = status == PaymentStatus.SUCCEEDED
            logger.info(f"Payment verification result: {verification_success}")
            
            return PaymentVerificationResponse(
                success=verification_success,
                payment_id=session_id,
                status=status,
                amount=self.format_currency_display(payment['amount'], payment['currency']),
                currency=payment['currency'],
                payment_method=self._map_razorpay_method(payment.get('method', 'card')),
                gateway_transaction_id=razorpay_payment_id,
                gateway_metadata={
                    'razorpay_payment_id': razorpay_payment_id,
                    'razorpay_order_id': razorpay_order_id,
                    'payment_details': payment,
                    'bank': payment.get('bank'),
                    'wallet': payment.get('wallet'),
                    'acquirer_data': payment.get('acquirer_data', {})
                }
            )
            
        except Exception as e:
            logger.error(f"Razorpay payment verification exception: {str(e)}", exc_info=True)
            return PaymentVerificationResponse(
                success=False,
                payment_id=session_id,
                status=PaymentStatus.FAILED,
                amount=0,
                currency="USD",
                payment_method="unknown",
                error_message=f"Payment verification failed: {str(e)}"
            )

    def _verify_razorpay_signature(self, order_id: str, payment_id: str, signature: str) -> bool:
        """Verify Razorpay payment signature using HMAC SHA256"""
        try:
            api_secret = self.config['api_secret']
            logger.debug(f"Using API secret: {api_secret[:10]}...")
            
            # Create the string to be signed
            body = f"{order_id}|{payment_id}"
            logger.debug(f"Signature payload: {body}")
            
            # Generate expected signature
            expected_signature = hmac.new(
                api_secret.encode('utf-8'),
                body.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            logger.debug(f"Expected signature: {expected_signature}")
            logger.debug(f"Received signature: {signature}")
            
            # Compare signatures
            result = hmac.compare_digest(expected_signature, signature)
            logger.info(f"Signature comparison result: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Signature verification error: {str(e)}")
            return False
    
    def get_payment_status(self, payment_id: str) -> PaymentVerificationResponse:
        """Get Razorpay payment status"""
        
        try:
            payment = self.client.payment.fetch(payment_id)
            status = self._map_razorpay_status(payment['status'])
            
            return PaymentVerificationResponse(
                success=status == PaymentStatus.SUCCEEDED,
                payment_id=payment_id,
                status=status,
                amount=self.format_currency_display(payment['amount'], payment['currency']),
                currency=payment['currency'],
                payment_method=self._map_razorpay_method(payment.get('method', 'card')),
                gateway_transaction_id=payment_id,
                gateway_metadata={
                    'payment_details': payment,
                    'status_details': payment.get('status_details', {}),
                    'created_at': payment.get('created_at'),
                    'bank': payment.get('bank'),
                    'wallet': payment.get('wallet')
                }
            )
            
        except Exception as e:
            return PaymentVerificationResponse(
                success=False,
                payment_id=payment_id,
                status=PaymentStatus.FAILED,
                amount=0,
                currency="USD",
                payment_method="unknown",
                error_message=str(e)
            )
    
    def refund_payment(self, request: RefundRequest) -> RefundResponse:
        """Process Razorpay refund"""
        
        try:
            # Convert amount to paise
            amount_paise = self.format_amount(request.amount)
            
            refund_data = {
                'amount': amount_paise,
                'notes': {
                    'reason': request.reason,
                    'refund_type': 'partial' if amount_paise else 'full'
                }
            }
            
            # Add metadata
            if request.metadata:
                refund_data['notes'].update(request.metadata)
            
            # Create refund
            refund = self.client.payment.refund(request.payment_id, refund_data)
            
            return RefundResponse(
                success=True,
                refund_id=refund['id'],
                amount=self.format_currency_display(refund['amount']),
                status=refund['status'],
                gateway_refund_id=refund['id'],
                gateway_response={
                    'refund_details': refund,
                    'speed_processed': refund.get('speed_processed'),
                    'speed_requested': refund.get('speed_requested')
                }
            )
            
        except Exception as e:
            return RefundResponse(
                success=False,
                refund_id="",
                amount=0,
                status="failed",
                error_message=str(e)
            )
    
    def handle_webhook(self, payload: Dict[str, Any], signature: str = "") -> Dict[str, Any]:
        """Handle Razorpay webhook events"""
        
        try:
            # Verify webhook signature
            if not self.is_webhook_signature_valid(json.dumps(payload), signature):
                return {
                    'success': False,
                    'error': 'Invalid webhook signature'
                }
            
            event = payload.get('event', '')
            entity = payload.get('payload', {}).get('payment', {}).get('entity', {})
            
            # Process different event types
            if event == 'payment.captured':
                return self._handle_payment_captured(entity)
            elif event == 'payment.failed':
                return self._handle_payment_failed(entity)
            elif event == 'order.paid':
                return self._handle_order_paid(entity)
            elif event == 'refund.created':
                return self._handle_refund_created(entity)
            elif event == 'subscription.charged':
                return self._handle_subscription_charged(entity)
            else:
                return {
                    'success': True,
                    'event_type': event,
                    'processed': False,
                    'message': f'Unhandled event type: {event}'
                }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'event_type': payload.get('event', 'unknown')
            }
    
    def cancel_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """Cancel Razorpay subscription"""
        
        try:
            subscription = self.client.subscription.cancel(subscription_id)
            
            return {
                'success': True,
                'subscription_id': subscription_id,
                'status': subscription['status'],
                'cancelled_at': subscription.get('cancelled_at'),
                'end_at': subscription.get('end_at')
            }
            
        except Exception as e:
            return {
                'success': False,
                'subscription_id': subscription_id,
                'error': str(e)
            }
    
    def _test_gateway_connection(self) -> Dict[str, Any]:
        """Test Razorpay connection"""
        
        try:
            # Try to fetch account details
            self.client.payment.all({'count': 1})
            
            return {
                'success': True,
                'gateway': 'razorpay',
                'connection_time': '< 1s',
                'api_version': 'v1'
            }
            
        except Exception as e:
            return {
                'success': False,
                'gateway': 'razorpay',
                'error': str(e)
            }
    
    def _map_razorpay_status(self, razorpay_status: str) -> PaymentStatus:
        """Map Razorpay status to our PaymentStatus"""
        
        status_mapping = {
            'created': PaymentStatus.CREATED,
            'authorized': PaymentStatus.SUCCEEDED,  # Authorized payments are successful for verification
            'captured': PaymentStatus.SUCCEEDED,
            'refunded': PaymentStatus.REFUNDED,
            'failed': PaymentStatus.FAILED
        }
        
        return status_mapping.get(razorpay_status, PaymentStatus.FAILED)
    
    def _map_razorpay_method(self, razorpay_method: str) -> str:
        """Map Razorpay payment method to our standard methods"""
        
        method_mapping = {
            'card': 'card',
            'netbanking': 'net_banking',
            'wallet': 'wallet',
            'upi': 'upi',
            'emi': 'emi',
            'cardless_emi': 'emi',
            'paylater': 'wallet'
        }
        
        return method_mapping.get(razorpay_method, 'card')
    
    def _handle_payment_captured(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """Handle payment captured webhook"""
        
        return {
            'success': True,
            'event_type': 'payment.captured',
            'payment_id': entity.get('id'),
            'order_id': entity.get('order_id'),
            'amount': entity.get('amount'),
            'status': 'captured',
            'processed': True
        }
    
    def _handle_payment_failed(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """Handle payment failed webhook"""
        
        return {
            'success': True,
            'event_type': 'payment.failed',
            'payment_id': entity.get('id'),
            'order_id': entity.get('order_id'),
            'error_reason': entity.get('error_reason'),
            'error_description': entity.get('error_description'),
            'status': 'failed',
            'processed': True
        }
    
    def _handle_order_paid(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """Handle order paid webhook"""
        
        return {
            'success': True,
            'event_type': 'order.paid',
            'order_id': entity.get('id'),
            'amount': entity.get('amount'),
            'status': 'paid',
            'processed': True
        }
    
    def _handle_refund_created(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """Handle refund created webhook"""
        
        return {
            'success': True,
            'event_type': 'refund.created',
            'refund_id': entity.get('id'),
            'payment_id': entity.get('payment_id'),
            'amount': entity.get('amount'),
            'status': entity.get('status'),
            'processed': True
        }
    
    def _handle_subscription_charged(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """Handle subscription charged webhook"""
        
        return {
            'success': True,
            'event_type': 'subscription.charged',
            'subscription_id': entity.get('subscription_id'),
            'payment_id': entity.get('id'),
            'amount': entity.get('amount'),
            'status': 'charged',
            'processed': True
        }
    
    def is_webhook_signature_valid(self, payload: str, signature: str) -> bool:
        """Validate Razorpay webhook signature"""
        
        if not self.webhook_secret or not signature:
            return False
        
        try:
            expected_signature = hmac.new(
                self.webhook_secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(expected_signature, signature)
            
        except Exception:
            return False
    
    def create_subscription(self, plan_id: str, customer_id: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create Razorpay subscription"""
        
        try:
            subscription_data = {
                'plan_id': plan_id,
                'customer_id': customer_id,
                'total_count': 12,  # Default to 12 payments
                'notify': {
                    'sms': True,
                    'email': True
                }
            }
            
            if metadata:
                subscription_data['notes'] = metadata
            
            subscription = self.client.subscription.create(subscription_data)
            
            return {
                'success': True,
                'subscription_id': subscription['id'],
                'status': subscription['status'],
                'subscription_details': subscription
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_payment_methods_config(self) -> Dict[str, Any]:
        """Get Razorpay payment methods configuration"""
        
        config = super().get_payment_methods_config()
        
        config.update({
            'razorpay_config': {
                'key': self.config['api_key'],
                'currency': 'USD',
                'theme_color': '#3399cc',
                'image': '/static/logo.png',
                'prefill': {
                    'method': 'card'
                },
                'notes': {
                    'gateway': 'razorpay'
                },
                'modal': {
                    'ondismiss': 'function(){console.log("checkout cancelled");}'
                }
            }
        })
        
        return config