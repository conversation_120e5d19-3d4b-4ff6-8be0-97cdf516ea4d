from typing import Dict, Any, Optional, List
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

from .base import PaymentGateway
from .razorpay_gateway import Ra<PERSON><PERSON>yGateway
from .stripe_gateway import StripeGateway


class PaymentGatewayFactory:
    """
    Factory class for creating payment gateway instances
    Implements strategy pattern for runtime gateway selection
    """
    
    _gateways = {
        'razorpay': RazorpayGateway,
        'stripe': StripeGateway,
    }
    
    @classmethod
    def create_gateway(cls, gateway_name: str, config: Optional[Dict[str, Any]] = None) -> PaymentGateway:
        """
        Create a payment gateway instance
        
        Args:
            gateway_name: Name of the gateway ('mock', 'razorpay', 'phonepe', etc.)
            config: Gateway-specific configuration (optional, will use settings if not provided)
            
        Returns:
            PaymentGateway instance
            
        Raises:
            ValueError: If gateway_name is not supported
            ImproperlyConfigured: If gateway configuration is invalid
        """
        
        if gateway_name not in cls._gateways:
            available_gateways = list(cls._gateways.keys())
            raise ValueError(
                f"Unsupported gateway: {gateway_name}. "
                f"Available gateways: {available_gateways}"
            )
        
        # Get configuration if not provided
        if config is None:
            config = cls._get_gateway_config(gateway_name)
        
        # Validate configuration
        gateway_class = cls._gateways[gateway_name]
        temp_gateway = gateway_class.__new__(gateway_class)
        temp_gateway.config = config
        
        validation_result = temp_gateway.validate_config()
        if not validation_result['valid']:
            raise ImproperlyConfigured(
                f"Invalid configuration for {gateway_name}: "
                f"Missing fields: {validation_result['missing_fields']}"
            )
        
        # Create and return gateway instance
        return gateway_class(config)
    
    @classmethod
    def get_default_gateway(cls) -> PaymentGateway:
        """
        Get the default payment gateway based on environment settings
        
        Returns:
            PaymentGateway instance for the default gateway
        """
        
        # Get default gateway from settings
        default_gateway = getattr(settings, 'DEFAULT_PAYMENT_GATEWAY', 'razorpay')
        
        return cls.create_gateway(default_gateway)
    
    @classmethod
    def get_available_gateways(cls) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all available payment gateways
        
        Returns:
            Dictionary with gateway information
        """
        
        gateways_info = {}
        
        for gateway_name, gateway_class in cls._gateways.items():
            try:
                # Try to create gateway with default config
                config = cls._get_gateway_config(gateway_name)
                gateway = gateway_class(config)
                
                gateways_info[gateway_name] = {
                    'name': gateway_name,
                    'display_name': gateway_name.title(),
                    'supported_features': gateway.get_supported_features(),
                    'supported_methods': gateway.get_supported_payment_methods(),
                    'available': True,
                    'config_valid': gateway.validate_config()['valid']
                }
                
            except Exception as e:
                gateways_info[gateway_name] = {
                    'name': gateway_name,
                    'display_name': gateway_name.title(),
                    'available': False,
                    'error': str(e)
                }
        
        return gateways_info
    
    @classmethod
    def test_gateway_connections(cls) -> Dict[str, Dict[str, Any]]:
        """
        Test connections to all configured gateways
        
        Returns:
            Dictionary with test results for each gateway
        """
        
        test_results = {}
        
        for gateway_name in cls._gateways.keys():
            try:
                gateway = cls.create_gateway(gateway_name)
                test_result = gateway.test_connection()
                test_results[gateway_name] = test_result
                
            except Exception as e:
                test_results[gateway_name] = {
                    'success': False,
                    'error': str(e),
                    'gateway': gateway_name
                }
        
        return test_results
    
    @classmethod
    def get_gateway_for_user(cls, user, preferred_gateway: str = None) -> PaymentGateway:
        """
        Get the best payment gateway for a specific user
        
        Args:
            user: User instance
            preferred_gateway: User's preferred gateway (optional)
            
        Returns:
            PaymentGateway instance
        """
        
        # If user has a preferred gateway and it's available, use it
        if preferred_gateway and preferred_gateway in cls._gateways:
            try:
                return cls.create_gateway(preferred_gateway)
            except Exception:
                pass  # Fall back to default logic
        
        # Check user's location/preferences for gateway selection
        # This could be extended to include location-based gateway selection
        
        # For now, return default gateway
        return cls.get_default_gateway()
    
    @classmethod
    def register_gateway(cls, name: str, gateway_class: type):
        """
        Register a new payment gateway
        
        Args:
            name: Gateway name identifier
            gateway_class: Gateway class that extends PaymentGateway
        """
        
        if not issubclass(gateway_class, PaymentGateway):
            raise ValueError("Gateway class must extend PaymentGateway")
        
        cls._gateways[name] = gateway_class
    
    @classmethod
    def get_gateway_by_transaction_id(cls, transaction_id: str) -> Optional[PaymentGateway]:
        """
        Get gateway instance based on transaction ID pattern
        
        Args:
            transaction_id: Gateway transaction ID
            
        Returns:
            PaymentGateway instance or None
        """
        
        # Different gateways have different transaction ID patterns
        if transaction_id.startswith('pay_'):
            # Razorpay transaction ID pattern
            return cls.create_gateway('razorpay')
        elif transaction_id.startswith('pi_') or transaction_id.startswith('in_'):
            # Stripe transaction ID pattern (for backward compatibility)
            return cls.create_gateway('stripe')
        
        # Default to configured gateway
        return cls.get_default_gateway()
    
    @classmethod
    def _get_gateway_config(cls, gateway_name: str) -> Dict[str, Any]:
        """
        Get configuration for a specific gateway from Django settings
        
        Args:
            gateway_name: Name of the gateway
            
        Returns:
            Configuration dictionary
        """
        
        if gateway_name == 'razorpay':
            return cls._get_razorpay_config()
        elif gateway_name == 'stripe':
            return cls._get_stripe_config()
        else:
            raise ValueError(f"No configuration method for gateway: {gateway_name}")
    
    @classmethod
    def _get_razorpay_config(cls) -> Dict[str, Any]:
        """Get Razorpay configuration from settings"""
        
        config = {
            'api_key': getattr(settings, 'RAZORPAY_API_KEY', ''),
            'api_secret': getattr(settings, 'RAZORPAY_API_SECRET', ''),
            'webhook_secret': getattr(settings, 'RAZORPAY_WEBHOOK_SECRET', ''),
            'test_mode': getattr(settings, 'RAZORPAY_TEST_MODE', settings.DEBUG)
        }
        
        if not config['api_key'] or not config['api_secret']:
            # Try environment variables
            import os
            config['api_key'] = os.getenv('RAZORPAY_API_KEY', '')
            config['api_secret'] = os.getenv('RAZORPAY_API_SECRET', '')
            config['webhook_secret'] = os.getenv('RAZORPAY_WEBHOOK_SECRET', '')
        
        return config
    
    @classmethod
    def _get_stripe_config(cls) -> Dict[str, Any]:
        """Get Stripe configuration from settings (for backward compatibility)"""
        
        return {
            'api_key': getattr(settings, 'STRIPE_SECRET_KEY', ''),
            'publishable_key': getattr(settings, 'STRIPE_PUBLIC_KEY', ''),
            'webhook_secret': getattr(settings, 'STRIPE_WEBHOOK_SECRET', ''),
            'test_mode': getattr(settings, 'STRIPE_TEST_MODE', settings.DEBUG)
        }


class PaymentGatewaySelector:
    """
    Advanced gateway selection based on various criteria
    """
    
    @staticmethod
    def select_optimal_gateway(
        amount: float,
        currency: str = "USD",
        user_location: str = "US",
        user_preferences: Dict[str, Any] = None
    ) -> str:
        """
        Select the optimal payment gateway based on various criteria
        
        Args:
            amount: Payment amount
            currency: Payment currency
            user_location: User's location/country code
            user_preferences: User's payment preferences
            
        Returns:
            Gateway name
        """
        
        if user_preferences is None:
            user_preferences = {}
        
        # For development/testing, prefer razorpay gateway
        if settings.DEBUG:
            return 'razorpay'
        
        # For USD payments, prefer Stripe
        if currency == "USD":
            return 'stripe'
        
        # Default gateway selection logic
        return getattr(settings, 'DEFAULT_PAYMENT_GATEWAY', 'stripe')
    
    @staticmethod
    def get_fallback_gateways(primary_gateway: str) -> List[str]:
        """
        Get list of fallback gateways if primary gateway fails
        
        Args:
            primary_gateway: Primary gateway name
            
        Returns:
            List of fallback gateway names
        """
        
        fallback_mapping = {
            'razorpay': ['stripe'],
            'stripe': ['razorpay']
        }
        
        return fallback_mapping.get(primary_gateway, [])