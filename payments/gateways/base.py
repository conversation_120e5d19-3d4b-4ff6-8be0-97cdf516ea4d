from abc import ABC, abstractmethod
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
import uuid
from datetime import datetime, timedelta
from django.utils import timezone

logger = logging.getLogger(__name__)

class PaymentStatus(Enum):
    """Standardized payment statuses across all gateways"""
    CREATED = "created"
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCEEDED = "succeeded"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"
    PARTIALLY_REFUNDED = "partially_refunded"
    EXPIRED = "expired"


class PaymentMethod(Enum):
    """Supported payment methods"""
    CARD = "card"
    UPI = "upi"
    NET_BANKING = "net_banking"
    WALLET = "wallet"
    EMI = "emi"
    BANK_TRANSFER = "bank_transfer"


@dataclass
class PaymentRequest:
    """Standardized payment request structure"""
    amount: float
    currency: str
    user_id: str
    package_id: str
    callback_url: str
    webhook_url: str
    metadata: Dict[str, Any] = None
    payment_methods: List[str] = None
    description: str = ""
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.payment_methods is None:
            self.payment_methods = ["card", "upi", "net_banking", "wallet"]


@dataclass
class PaymentResponse:
    """Standardized payment response structure"""
    success: bool
    session_id: str
    gateway_session_id: str = ""
    payment_url: str = ""
    status: PaymentStatus = PaymentStatus.CREATED
    gateway_response: Dict[str, Any] = None
    error_message: str = ""
    error_code: str = ""
    
    def __post_init__(self):
        if self.gateway_response is None:
            self.gateway_response = {}


@dataclass
class PaymentVerificationResponse:
    """Standardized payment verification response"""
    success: bool
    payment_id: str
    status: PaymentStatus
    amount: float
    currency: str
    payment_method: str
    gateway_transaction_id: str = ""
    gateway_metadata: Dict[str, Any] = None
    error_message: str = ""
    
    def __post_init__(self):
        if self.gateway_metadata is None:
            self.gateway_metadata = {}


@dataclass
class RefundRequest:
    """Standardized refund request structure"""
    payment_id: str
    amount: float
    reason: str = ""
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class RefundResponse:
    """Standardized refund response structure"""
    success: bool
    refund_id: str
    amount: float
    status: str
    gateway_refund_id: str = ""
    gateway_response: Dict[str, Any] = None
    error_message: str = ""
    
    def __post_init__(self):
        if self.gateway_response is None:
            self.gateway_response = {}


class PaymentGateway(ABC):
    """
    Abstract base class for all payment gateways
    Defines the standard interface that all payment gateways must implement
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the payment gateway with configuration
        
        Args:
            config: Gateway-specific configuration including API keys, URLs, etc.
        """
        self.config = config
        self.gateway_name = self.get_gateway_name()
        self.is_test_mode = config.get('test_mode', False)
    
    @abstractmethod
    def get_gateway_name(self) -> str:
        """Return the gateway name identifier"""
        pass
    
    @abstractmethod
    def get_supported_features(self) -> Dict[str, bool]:
        """
        Return a dictionary of supported features
        
        Returns:
            Dict with features like:
            {
                'subscriptions': True,
                'refunds': True,
                'partial_refunds': True,
                'webhooks': True,
                'upi': True,
                'cards': True,
                'net_banking': True,
                'wallets': True,
                'emi': False
            }
        """
        pass
    
    @abstractmethod
    def create_payment_session(self, request: PaymentRequest) -> PaymentResponse:
        """
        Create a payment session/checkout
        
        Args:
            request: PaymentRequest object with payment details
            
        Returns:
            PaymentResponse object with session details
        """
        pass
    
    @abstractmethod
    def verify_payment(self, session_id: str, gateway_data: Dict[str, Any] = None) -> PaymentVerificationResponse:
        """
        Verify payment completion
        
        Args:
            session_id: Internal session ID
            gateway_data: Gateway-specific data for verification
            
        Returns:
            PaymentVerificationResponse object with verification details
        """
        pass
    
    @abstractmethod
    def get_payment_status(self, payment_id: str) -> PaymentVerificationResponse:
        """
        Get current payment status
        
        Args:
            payment_id: Payment ID or transaction ID
            
        Returns:
            PaymentVerificationResponse object with current status
        """
        pass
    
    @abstractmethod
    def refund_payment(self, request: RefundRequest) -> RefundResponse:
        """
        Process payment refund
        
        Args:
            request: RefundRequest object with refund details
            
        Returns:
            RefundResponse object with refund details
        """
        pass
    
    @abstractmethod
    def handle_webhook(self, payload: Dict[str, Any], signature: str = "") -> Dict[str, Any]:
        """
        Handle webhook events from the gateway
        
        Args:
            payload: Webhook payload
            signature: Webhook signature for verification
            
        Returns:
            Dictionary with processed webhook data
        """
        pass
    
    @abstractmethod
    def cancel_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """
        Cancel a subscription
        
        Args:
            subscription_id: Subscription ID to cancel
            
        Returns:
            Dictionary with cancellation result
        """
        pass
    
    def get_supported_payment_methods(self) -> List[str]:
        """
        Get list of supported payment methods
        
        Returns:
            List of supported payment method codes
        """
        features = self.get_supported_features()
        methods = []
        
        if features.get('cards', False):
            methods.append('card')
        if features.get('upi', False):
            methods.append('upi')
        if features.get('net_banking', False):
            methods.append('net_banking')
        if features.get('wallets', False):
            methods.append('wallet')
        if features.get('emi', False):
            methods.append('emi')
        if features.get('bank_transfer', False):
            methods.append('bank_transfer')
            
        return methods
    
    def generate_session_id(self) -> str:
        """Generate a unique session ID"""
        return f"{self.gateway_name}_{uuid.uuid4().hex[:16]}"
    
    def validate_config(self) -> Dict[str, Any]:
        """
        Validate gateway configuration
        
        Returns:
            Dictionary with validation result
        """
        required_fields = self.get_required_config_fields()
        missing_fields = []
        
        for field in required_fields:
            logger.error(f"Validating config field: {self.config.get(field)}")
            if not self.config.get(field):
                missing_fields.append(field)
        
        return {
            'valid': len(missing_fields) == 0,
            'missing_fields': missing_fields,
            'gateway': self.get_gateway_name()
        }
    
    @abstractmethod
    def get_required_config_fields(self) -> List[str]:
        """
        Get list of required configuration fields
        
        Returns:
            List of required configuration field names
        """
        pass
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Test gateway connection and configuration
        
        Returns:
            Dictionary with test result
        """
        try:
            # Validate configuration first
            validation = self.validate_config()
            if not validation['valid']:
                return {
                    'success': False,
                    'error': f"Invalid configuration: {validation['missing_fields']}"
                }
            
            # Perform gateway-specific connection test
            return self._test_gateway_connection()
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    @abstractmethod
    def _test_gateway_connection(self) -> Dict[str, Any]:
        """
        Gateway-specific connection test implementation
        
        Returns:
            Dictionary with test result
        """
        pass
    
    def log_transaction(self, action: str, data: Dict[str, Any]):
        """
        Log transaction for debugging and monitoring
        
        Args:
            action: Action being performed
            data: Transaction data
        """
        # Implementation for logging transactions
        # This could be extended to integrate with logging systems
        pass
    
    def format_amount(self, amount: float, currency: str = "USD") -> int:
        """
        Format amount according to gateway requirements
        
        Args:
            amount: Amount in primary currency unit
            currency: Currency code
            
        Returns:
            Amount in smallest currency unit (cents for USD, paise for INR)
        """
        # Convert to smallest currency unit
        if currency.upper() == "USD":
            return int(amount * 100)  # Convert to cents
        elif currency.upper() == "INR":
            return int(amount * 100)  # Convert to paise
        return int(amount * 100)  # Default to cents
    
    def format_currency_display(self, amount: int, currency: str = "USD") -> float:
        """
        Format amount for display purposes
        
        Args:
            amount: Amount in smallest currency unit
            currency: Currency code
            
        Returns:
            Amount in primary currency unit
        """
        if currency.upper() == "USD":
            return amount / 100  # Convert from cents to dollars
        elif currency.upper() == "INR":
            return amount / 100  # Convert from paise to rupees
        return amount / 100  # Default conversion
    
    def get_webhook_url(self, base_url: str) -> str:
        """
        Generate webhook URL for this gateway
        
        Args:
            base_url: Base URL of the application
            
        Returns:
            Complete webhook URL
        """
        return f"{base_url.rstrip('/')}/payments/webhook/{self.gateway_name}"
    
    def is_webhook_signature_valid(self, payload: str, signature: str) -> bool:
        """
        Validate webhook signature
        
        Args:
            payload: Raw webhook payload
            signature: Signature to validate
            
        Returns:
            True if signature is valid
        """
        # Default implementation - should be overridden by specific gateways
        return True
    
    def get_payment_methods_config(self) -> Dict[str, Any]:
        """
        Get payment methods configuration for frontend
        
        Returns:
            Dictionary with payment methods configuration
        """
        return {
            'supported_methods': self.get_supported_payment_methods(),
            'gateway_name': self.gateway_name,
            'test_mode': self.is_test_mode,
            'features': self.get_supported_features()
        }