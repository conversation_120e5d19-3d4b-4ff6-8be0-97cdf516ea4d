"""
Stripe Payment Gateway Implementation
Supports Stripe's payment processing with multi-gateway architecture
"""

import logging
from typing import Dict, Any, List
import stripe
import hashlib
import hmac
from datetime import datetime, timedelta
from django.utils import timezone

from .base import (
    PaymentGateway, PaymentRequest, PaymentResponse, PaymentVerificationResponse,
    RefundRequest, RefundResponse, PaymentStatus, PaymentMethod
)

logger = logging.getLogger(__name__)


class StripeGateway(PaymentGateway):
    """
    Stripe payment gateway implementation
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        stripe.api_key = config.get('api_key', '')
        self.publishable_key = config.get('publishable_key', '')
        self.webhook_secret = config.get('webhook_secret', '')
    
    def get_gateway_name(self) -> str:
        return "stripe"
    
    def get_supported_features(self) -> Dict[str, bool]:
        return {
            'subscriptions': True,
            'refunds': True,
            'partial_refunds': True,
            'webhooks': True,
            'cards': True,
            'upi': False,  # Stripe doesn't support UPI directly
            'net_banking': False,
            'wallets': True,  # Limited wallet support
            'emi': False,
            'bank_transfer': True  # ACH transfers
        }
    
    def get_required_config_fields(self) -> List[str]:
        return ['api_key', 'publishable_key', 'webhook_secret']
    
    def create_payment_session(self, request: PaymentRequest) -> PaymentResponse:
        """
        Create a Stripe checkout session
        """
        try:
            # Generate internal session ID
            session_id = self.generate_session_id()
            
            # Create or get Stripe customer
            customer_id = self._get_or_create_customer(request)
            
            # Determine payment mode and create session
            if self._is_subscription_payment(request):
                stripe_session = self._create_subscription_session(request, customer_id, session_id)
            else:
                stripe_session = self._create_one_time_session(request, customer_id, session_id)
            
            return PaymentResponse(
                success=True,
                session_id=session_id,
                gateway_session_id=stripe_session.id,
                payment_url=stripe_session.url,
                status=PaymentStatus.CREATED,
                gateway_response={
                    'stripe_session_id': stripe_session.id,
                    'customer_id': customer_id,
                    'publishable_key': self.publishable_key,
                    'mode': stripe_session.mode
                }
            )
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating session: {str(e)}")
            return PaymentResponse(
                success=False,
                session_id="",
                error_message=str(e),
                error_code=getattr(e, 'code', 'stripe_error')
            )
        except Exception as e:
            logger.error(f"Unexpected error creating Stripe session: {str(e)}")
            return PaymentResponse(
                success=False,
                session_id="",
                error_message=str(e),
                error_code="unexpected_error"
            )
    
    def verify_payment(self, session_id: str, gateway_data: Dict[str, Any] = None) -> PaymentVerificationResponse:
        """
        Verify Stripe payment completion
        """
        try:
            if not gateway_data or 'stripe_session_id' not in gateway_data:
                return PaymentVerificationResponse(
                    success=False,
                    payment_id="",
                    status=PaymentStatus.FAILED,
                    amount=0,
                    currency="",
                    payment_method="",
                    error_message="Missing Stripe session ID"
                )
            
            stripe_session_id = gateway_data['stripe_session_id']
            
            # Retrieve the session from Stripe
            session = stripe.checkout.Session.retrieve(stripe_session_id)
            
            if session.payment_status == 'paid':
                payment_id = session.payment_intent
                payment_method = self._get_payment_method_from_session(session)
                
                return PaymentVerificationResponse(
                    success=True,
                    payment_id=payment_id,
                    status=PaymentStatus.SUCCEEDED,
                    amount=session.amount_total / 100,  # Convert from cents
                    currency=session.currency.upper(),
                    payment_method=payment_method,
                    gateway_transaction_id=payment_id,
                    gateway_metadata={
                        'stripe_session_id': stripe_session_id,
                        'customer_id': session.customer,
                        'subscription_id': session.subscription
                    }
                )
            else:
                status = self._map_stripe_status(session.payment_status)
                return PaymentVerificationResponse(
                    success=False,
                    payment_id=session.payment_intent or "",
                    status=status,
                    amount=session.amount_total / 100,
                    currency=session.currency.upper(),
                    payment_method="",
                    error_message=f"Payment not completed. Status: {session.payment_status}"
                )
                
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error verifying payment: {str(e)}")
            return PaymentVerificationResponse(
                success=False,
                payment_id="",
                status=PaymentStatus.FAILED,
                amount=0,
                currency="",
                payment_method="",
                error_message=str(e)
            )
        except Exception as e:
            logger.error(f"Unexpected error verifying Stripe payment: {str(e)}")
            return PaymentVerificationResponse(
                success=False,
                payment_id="",
                status=PaymentStatus.FAILED,
                amount=0,
                currency="",
                payment_method="",
                error_message=str(e)
            )
    
    def get_payment_status(self, payment_id: str) -> PaymentVerificationResponse:
        """
        Get current payment status from Stripe
        """
        try:
            payment_intent = stripe.PaymentIntent.retrieve(payment_id)
            
            return PaymentVerificationResponse(
                success=payment_intent.status == 'succeeded',
                payment_id=payment_intent.id,
                status=self._map_stripe_status(payment_intent.status),
                amount=payment_intent.amount / 100,
                currency=payment_intent.currency.upper(),
                payment_method=self._get_payment_method_from_payment_intent(payment_intent),
                gateway_transaction_id=payment_intent.id,
                gateway_metadata={
                    'payment_intent_id': payment_intent.id,
                    'customer_id': payment_intent.customer,
                    'charges': [charge.id for charge in payment_intent.charges.data]
                }
            )
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error getting payment status: {str(e)}")
            return PaymentVerificationResponse(
                success=False,
                payment_id=payment_id,
                status=PaymentStatus.FAILED,
                amount=0,
                currency="",
                payment_method="",
                error_message=str(e)
            )
    
    def refund_payment(self, request: RefundRequest) -> RefundResponse:
        """
        Process refund through Stripe
        """
        try:
            refund = stripe.Refund.create(
                payment_intent=request.payment_id,
                amount=int(request.amount * 100),  # Convert to cents
                reason=request.reason or 'requested_by_customer',
                metadata=request.metadata
            )
            
            return RefundResponse(
                success=True,
                refund_id=refund.id,
                amount=refund.amount / 100,
                status=refund.status,
                gateway_refund_id=refund.id,
                gateway_response={
                    'refund_id': refund.id,
                    'payment_intent': refund.payment_intent,
                    'charge': refund.charge,
                    'created': refund.created
                }
            )
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error processing refund: {str(e)}")
            return RefundResponse(
                success=False,
                refund_id="",
                amount=request.amount,
                status="failed",
                error_message=str(e)
            )
    
    def handle_webhook(self, payload: Dict[str, Any], signature: str = "") -> Dict[str, Any]:
        """
        Handle Stripe webhook events
        """
        try:
            # Verify webhook signature
            event = stripe.Webhook.construct_event(
                payload, signature, self.webhook_secret
            )
            
            event_type = event['type']
            event_data = event['data']['object']
            
            result = {
                'success': True,
                'event_type': event_type,
                'processed': False
            }
            
            if event_type == 'checkout.session.completed':
                result.update(self._handle_checkout_completed(event_data))
            elif event_type == 'invoice.paid':
                result.update(self._handle_invoice_paid(event_data))
            elif event_type == 'customer.subscription.deleted':
                result.update(self._handle_subscription_deleted(event_data))
            elif event_type == 'payment_intent.succeeded':
                result.update(self._handle_payment_succeeded(event_data))
            elif event_type == 'payment_intent.payment_failed':
                result.update(self._handle_payment_failed(event_data))
            else:
                result['message'] = f"Unhandled event type: {event_type}"
            
            return result
            
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Stripe webhook signature verification failed: {str(e)}")
            return {
                'success': False,
                'error': 'Invalid signature',
                'processed': False
            }
        except Exception as e:
            logger.error(f"Error processing Stripe webhook: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processed': False
            }
    
    def cancel_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """
        Cancel Stripe subscription
        """
        try:
            subscription = stripe.Subscription.delete(subscription_id)
            
            return {
                'success': True,
                'subscription_id': subscription.id,
                'status': subscription.status,
                'canceled_at': subscription.canceled_at
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error canceling subscription: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _test_gateway_connection(self) -> Dict[str, Any]:
        """
        Test Stripe connection
        """
        try:
            # Try to retrieve account information
            account = stripe.Account.retrieve()
            
            return {
                'success': True,
                'account_id': account.id,
                'country': account.country,
                'currency': account.default_currency
            }
            
        except stripe.error.StripeError as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_or_create_customer(self, request: PaymentRequest) -> str:
        """
        Get or create Stripe customer
        """
        # This would typically involve checking if the user already has a Stripe customer ID
        # For now, we'll create a new customer each time
        # In a real implementation, you'd store the customer ID in the user model
        
        customer = stripe.Customer.create(
            metadata={
                'user_id': request.user_id,
                'package_id': request.package_id
            }
        )
        
        return customer.id
    
    def _is_subscription_payment(self, request: PaymentRequest) -> bool:
        """
        Determine if this is a subscription payment
        """
        # This would check the package type or metadata
        # For now, assume it's based on metadata
        return request.metadata.get('billing_cycle') in ['monthly', 'yearly']
    
    def _create_subscription_session(self, request: PaymentRequest, customer_id: str, session_id: str) -> stripe.checkout.Session:
        """
        Create Stripe subscription checkout session
        """
        # Create or get price ID for subscription
        price_id = self._get_or_create_price(request)
        
        return stripe.checkout.Session.create(
            customer=customer_id,
            payment_method_types=['card'],
            line_items=[{
                'price': price_id,
                'quantity': 1,
            }],
            mode='subscription',
            success_url=request.callback_url + '?status=success',
            cancel_url=request.callback_url + '?status=cancelled',
            metadata={
                'session_id': session_id,
                'user_id': request.user_id,
                'package_id': request.package_id,
                **request.metadata
            }
        )
    
    def _create_one_time_session(self, request: PaymentRequest, customer_id: str, session_id: str) -> stripe.checkout.Session:
        """
        Create Stripe one-time payment checkout session
        """
        return stripe.checkout.Session.create(
            customer=customer_id,
            payment_method_types=['card'],
            line_items=[{
                'price_data': {
                    'currency': request.currency.lower(),
                    'product_data': {
                        'name': request.description or f"Package {request.package_id}",
                    },
                    'unit_amount': int(request.amount * 100),  # Convert to cents
                },
                'quantity': 1,
            }],
            mode='payment',
            success_url=request.callback_url + '?status=success',
            cancel_url=request.callback_url + '?status=cancelled',
            metadata={
                'session_id': session_id,
                'user_id': request.user_id,
                'package_id': request.package_id,
                **request.metadata
            }
        )
    
    def _get_or_create_price(self, request: PaymentRequest) -> str:
        """
        Get or create Stripe price for subscription
        """
        # This would typically check if a price already exists for this package
        # For now, create a new price each time
        
        billing_cycle = request.metadata.get('billing_cycle', 'monthly')
        interval = 'month' if billing_cycle == 'monthly' else 'year'
        
        product = stripe.Product.create(
            name=request.description or f"Package {request.package_id}",
            metadata={
                'package_id': request.package_id
            }
        )
        
        price = stripe.Price.create(
            product=product.id,
            unit_amount=int(request.amount * 100),
            currency=request.currency.lower(),
            recurring={'interval': interval},
            metadata={
                'package_id': request.package_id
            }
        )
        
        return price.id
    
    def _get_payment_method_from_session(self, session) -> str:
        """
        Extract payment method from Stripe session
        """
        if session.payment_method_types:
            method = session.payment_method_types[0]
            return 'card' if method == 'card' else method
        return 'card'
    
    def _get_payment_method_from_payment_intent(self, payment_intent) -> str:
        """
        Extract payment method from Stripe payment intent
        """
        if payment_intent.charges.data:
            charge = payment_intent.charges.data[0]
            if charge.payment_method_details:
                return charge.payment_method_details.type
        return 'card'
    
    def _map_stripe_status(self, stripe_status: str) -> PaymentStatus:
        """
        Map Stripe status to our standard PaymentStatus
        """
        status_mapping = {
            'succeeded': PaymentStatus.SUCCEEDED,
            'pending': PaymentStatus.PENDING,
            'requires_payment_method': PaymentStatus.FAILED,
            'requires_confirmation': PaymentStatus.PENDING,
            'requires_action': PaymentStatus.PENDING,
            'processing': PaymentStatus.PROCESSING,
            'requires_capture': PaymentStatus.PENDING,
            'canceled': PaymentStatus.CANCELLED,
            'paid': PaymentStatus.SUCCEEDED,
            'unpaid': PaymentStatus.PENDING,
            'no_payment_required': PaymentStatus.SUCCEEDED
        }
        
        return status_mapping.get(stripe_status, PaymentStatus.FAILED)
    
    def _handle_checkout_completed(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle checkout.session.completed event
        """
        return {
            'processed': True,
            'action': 'checkout_completed',
            'session_id': session_data.get('id'),
            'customer_id': session_data.get('customer'),
            'subscription_id': session_data.get('subscription'),
            'payment_intent_id': session_data.get('payment_intent'),
            'amount': session_data.get('amount_total', 0) / 100,
            'currency': session_data.get('currency', '').upper(),
            'metadata': session_data.get('metadata', {})
        }
    
    def _handle_invoice_paid(self, invoice_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle invoice.paid event (subscription renewal)
        """
        return {
            'processed': True,
            'action': 'invoice_paid',
            'subscription_id': invoice_data.get('subscription'),
            'customer_id': invoice_data.get('customer'),
            'amount': invoice_data.get('amount_paid', 0) / 100,
            'currency': invoice_data.get('currency', '').upper(),
            'invoice_id': invoice_data.get('id')
        }
    
    def _handle_subscription_deleted(self, subscription_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle customer.subscription.deleted event
        """
        return {
            'processed': True,
            'action': 'subscription_deleted',
            'subscription_id': subscription_data.get('id'),
            'customer_id': subscription_data.get('customer'),
            'canceled_at': subscription_data.get('canceled_at')
        }
    
    def _handle_payment_succeeded(self, payment_intent_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle payment_intent.succeeded event
        """
        return {
            'processed': True,
            'action': 'payment_succeeded',
            'payment_intent_id': payment_intent_data.get('id'),
            'customer_id': payment_intent_data.get('customer'),
            'amount': payment_intent_data.get('amount', 0) / 100,
            'currency': payment_intent_data.get('currency', '').upper()
        }
    
    def _handle_payment_failed(self, payment_intent_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle payment_intent.payment_failed event
        """
        return {
            'processed': True,
            'action': 'payment_failed',
            'payment_intent_id': payment_intent_data.get('id'),
            'customer_id': payment_intent_data.get('customer'),
            'amount': payment_intent_data.get('amount', 0) / 100,
            'currency': payment_intent_data.get('currency', '').upper(),
            'failure_reason': payment_intent_data.get('last_payment_error', {}).get('message', '')
        }