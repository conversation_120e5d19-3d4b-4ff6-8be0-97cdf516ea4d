# Core Django Settings
SECRET_KEY=django-insecure-supersecretkey123456789
DEBUG=True
LOG_LEVEL=INFO
ALLOWED_HOSTS=*,localhost,127.0.0.1

BASE_URL=https://videoai.syncu.in

# Database Configuration
DB_NAME=videoagent
DB_USER=postgres
DB_PASSWORD=password
DB_HOST=host.docker.internal
DB_PORT=5432

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.zoho.in
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=SyncU@2025
DEFAULT_FROM_EMAIL=<EMAIL>
STRIPE_PUBLIC_KEY=pk_test_yourstripepublickey
STRIPE_SECRET_KEY=sk_test_yourstripesecretkey
STRIPE_WEBHOOK_SECRET=whsec_yourstripewebhooksecret
CORS_ALLOW_ALL_ORIGINS=True

CALLBACK_BASE_URL=https://videoai.syncu.in/api/videos/callback/recieve-response/

# N8N Webhook Configuration
N8N_WEBHOOK_BASE_URL=https://n8n.syncu.in/webhook/
N8N_CREATE_VIDEO_API=a8a2c6b9-36dd-48f7-b4ef-7e83907afa74/video-agent/
N8N_WEBHOOK_JWT_SECRET=

# Google OAuth Configuration
GOOGLE_CALLBACK_URL=http://localhost:8000/api/auth/google/callback/

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=kafka:9092

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379/0

# Storage Configuration
STORAGE_BASE_URL=https://miniio-api.syncu.in/nca/

# NCA Toolkit API Configuration
NCA_API_BASE_URL=https://nca-toolkit.syncu.in/
NCA_API_KEY=
NCA_REQUEST_TIMEOUT=60

# RunPod Configuration
RUNPOD_API_KEY=
RUNPOD_BASE_URL=https://api.runpod.ai/v2/
RUNPOD_ENDPOINT_ID=
RUNPOD_REQUEST_TIMEOUT=60
RUNPOD_MAX_RETRIES=3
RUNPOD_POLL_INTERVAL=5

# Together AI Configuration
TOGETHER_AI_API_ENDPOINT=https://api.together.ai/v1/images/generations
TOGETHER_AI_API_KEY=
TOGETHER_AI_REQUEST_TIMEOUT=60
TOGETHER_AI_DEFAULT_MODEL=black-forest-labs/FLUX.1-schnell
TOGETHER_AI_MAX_RETRIES=3
