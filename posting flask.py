import os
import time
import requests
from flask import Flask, jsonify, request

app = Flask(__name__)

# ─── Configuration (fill these in) ─────────────────────────────────────────────
BASEROW_PROD_API_URL   = "https://api.baserow.io/api/database/rows/table/693/"
BASEROW_STAGING_API_URL= "https://api.baserow.io/api/database/rows/table/683/"
BASEROW_TOKEN          = os.getenv("BASEROW_TOKEN")

FB_PAGE_ID             = "***************"
FB_IG_ACCOUNT_ID       = "*****************"
FB_GRAPH_VERSION       = "v22.0"
FB_TOKEN               = os.getenv("FB_TOKEN")

YOUTUBE_CREDENTIALS    = {}   # e.g. google.oauth2.credentials.Credentials(...)
YOUTUBE_UPLOAD_URL     = "https://www.googleapis.com/upload/youtube/v3/videos"
# ────────────────────────────────────────────────────────────────────────────────

def baserow_get(url, row_id):
    resp = requests.get(
        f"{url}?user_field_names=true&row_id={row_id}",
        headers={"Authorization": f"Token {BASEROW_TOKEN}"}
    )
    resp.raise_for_status()
    return resp.json()["results"][0]

def baserow_update(url, row_id, field_id, value):
    body = {"field_values": {str(field_id): value}}
    resp = requests.patch(
        f"{url}{row_id}/",
        json=body,
        headers={"Authorization": f"Token {BASEROW_TOKEN}"}
    )
    resp.raise_for_status()
    return resp.json()

def post_facebook_photo(row):
    # download binary
    video_url = row["Production Video URL"]
    bin_resp = requests.get(video_url)
    bin_resp.raise_for_status()
    files = {"source": bin_resp.content}
    data = {"message": row["description"]}
    resp = requests.post(
        f"https://graph.facebook.com/{FB_GRAPH_VERSION}/{FB_PAGE_ID}/photos",
        params={"access_token": FB_TOKEN},
        data=data,
        files=files
    )
    resp.raise_for_status()
    # update staging status
    return baserow_update(BASEROW_STAGING_API_URL, row["id"], 6627, "posted")

def post_instagram_reel(row):
    # step 1: create media object
    params = {
        "video_url": row["Production Video URL"],
        "caption": row["description"],
        "cover_url": row["Production Video URL"],
        "media_type": "REELS",
        "access_token": FB_TOKEN
    }
    resp = requests.post(
        f"https://graph.facebook.com/{FB_GRAPH_VERSION}/{FB_IG_ACCOUNT_ID}/media",
        params=params
    )
    resp.raise_for_status()
    creation_id = resp.json()["id"]

    # step 2: wait 2 minutes
    time.sleep(120)

    # step 3: publish
    pub = requests.post(
        f"https://graph.facebook.com/{FB_GRAPH_VERSION}/{FB_IG_ACCOUNT_ID}/media_publish",
        params={"creation_id": creation_id, "access_token": FB_TOKEN}
    )
    pub.raise_for_status()
    # update staging status
    return baserow_update(BASEROW_STAGING_API_URL, row["id"], 6627, "posted")

def upload_youtube(row):
    # decide account
    account = row["Account"][0]["value"]
    title_tag = {
        "Bakthi Payanam":   "#BhaktiPayanam",
        "God Trail India":  "#GodTrailIndia",
        "Bhagwant Katha":   "#BhagwantKatha",
    }[account]

    # 1) initiate resumable upload
    snippet = {
      "snippet": {
        "title": f"{row['title']} | {title_tag}",
        "description": row["description"],
        "tags": row["description"].match(r"#[^ ]*") or [],
        "categoryId": "22",
        "defaultLanguage": row.get("language","en"),
        "defaultAudioLanguage": row.get("language","en")
      },
      "status": {
        "privacyStatus": "private",
        "publishAt": row.get("Publish at") or "",
        "license": "youtube",
        "embeddable": True,
        "publicStatsViewable": True,
        "madeForKids": False
      }
    }
    headers = {
      "Content-Type": "application/json",
      "X-Upload-Content-Type": "video/webm"
    }
    session = requests.post(
      f"{YOUTUBE_UPLOAD_URL}?part=snippet,status&uploadType=resumable",
      headers=headers,
      json=snippet,
      # auth=YOUTUBE_CREDENTIALS  ← configure google‐oauth here
    )
    session.raise_for_status()
    upload_url = session.headers["Location"]

    # 2) download binary & upload
    bin_resp = requests.get(row["Production Video URL"])
    bin_resp.raise_for_status()
    up = requests.put(
      upload_url,
      headers={"Content-Type": "video/webm"},
      data=bin_resp.content,
      # auth=YOUTUBE_CREDENTIALS
    )
    up.raise_for_status()

    # 3) update status in production Baserow
    return baserow_update(BASEROW_PROD_API_URL, row["Record ID"], 6834, "Posted")

@app.route("/post/<row_id>", methods=["POST"])
def webhook(row_id):
    row = baserow_get(BASEROW_PROD_API_URL, row_id)
    for p in row["Platforms"]:
        platform = p.lower()
        if platform == "facebook":
            post_facebook_photo(row)
        elif platform == "instagram":
            post_instagram_reel(row)
        elif platform == "youtube":
            upload_youtube(row)
    return jsonify({"status": "done"})

if __name__ == "__main__":
    app.run(port=8000)