from django.shortcuts import render
from rest_framework import viewsets, permissions
from rest_framework.response import Response
from rest_framework import status
from .models import Account
from .serializers import AccountSerializer, AccountListSerializer
from videoagent.pagination import CustomPagination


class AccountViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing user accounts.
    """
    serializer_class = AccountSerializer
    
    def get_queryset(self):
        """
        Return accounts for the current authenticated user.
        """
        return Account.objects.filter(user=self.request.user)
    
    def get_serializer_class(self):
        """
        Return different serializers for list and detail views.
        """
        if self.action == 'list':
            return AccountListSerializer
        return AccountSerializer
    
    def perform_create(self, serializer):
        """
        Associate the account with the current user when creating a new account.
        """
        serializer.save(user=self.request.user)
    
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        # Check if the account has associated videos
        if instance.videos.exists():
            return Response(
                {"detail": "Cannot delete account because it has associated videos."},
                status=status.HTTP_400_BAD_REQUEST
            )
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)
