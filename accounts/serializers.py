from rest_framework import serializers
from .models import Account
from authentication.serializers import UserSerializer

class AccountSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = Account
        fields = '__all__'
        read_only_fields = ('id', 'user', 'created_at')
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # if we're updating (instance exists) make topic optional
        if self.instance is not None:
            self.fields['topic'].required = False
        
    def create(self, validated_data):
        account = Account.objects.create(**validated_data)
        return account
        
        
class AccountListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Account
        fields = ('id', 'name', 'topic', 'platforms', 'language', 'status', 'created_at')
        read_only_fields = ('id', 'created_at')