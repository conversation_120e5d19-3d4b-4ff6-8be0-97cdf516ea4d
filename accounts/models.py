from django.db import models
from django.contrib.postgres.fields import Array<PERSON>ield
from django.utils import timezone
from authentication.models import User
from videos.constants import LANGUAGE_CHOICES

class Account(models.Model):
    class Meta:
        db_table = 'accounts'

    STATUS_CHOICES = (
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='accounts')
    name = models.Char<PERSON>ield(max_length=255)
    topic = models.CharField(max_length=255)
    platforms = ArrayField(models.CharField(max_length=50), blank=True, default=list)
    credentials = models.JSONField(default=dict, blank=True)
    language = models.CharField(max_length=50, default='en', choices=LANGUAGE_CHOICES)
    status = models.Char<PERSON>ield(max_length=10, choices=STATUS_CHOICES, default='active')
    created_at = models.DateTimeField(default=timezone.now)
    
    def __str__(self):
        return f"{self.name} - {self.user.email}"
