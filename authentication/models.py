from django.db import models
from django.contrib.auth.models import Abstract<PERSON><PERSON><PERSON><PERSON>, BaseUserManager, PermissionsMixin
from django.utils import timezone


class UserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('status', 'active')
        extra_fields.setdefault('username', email)  # Set username for superuser

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self.create_user(email, password, **extra_fields)


class User(AbstractBaseUser, PermissionsMixin):
    class Meta:
        db_table = 'users'

    STATUS_CHOICES = (
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    )
    
    username = models.CharField(max_length=150, unique=True, blank=True, null=True)  # Required for allauth
    email = models.EmailField(unique=True)
    name = models.CharField(max_length=255)
    mobile = models.CharField(max_length=20, blank=True, null=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='active')
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    date_joined = models.DateTimeField(default=timezone.now)
    email_verified_at = models.DateTimeField(blank=True, null=True)
    
    objects = UserManager()
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['name']
    
    def __str__(self):
        return self.email
    
    def get_active_package(self):
        """Get user's currently active package"""
        return self.user_packages.filter(status='active').first()
    
    def get_usage_summary(self):
        """Get user's current usage summary"""
        from payments.services import UsageService
        
        active_package = self.get_active_package()
        if not active_package:
            return None
        
        return UsageService.get_usage_summary(active_package)
    
    def get_remaining_limits(self):
        """Get all remaining limits for the user"""
        active_package = self.get_active_package()
        if not active_package:
            return {}
        
        limits = active_package.package.features.get('limits', {})
        remaining = {}
        
        for usage_type, limit in limits.items():
            remaining[usage_type] = active_package.get_remaining_limit(usage_type)
        
        return remaining


class UserConfig(models.Model):
    class Meta:
        db_table = 'user_configs'

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_configs')
    config = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.user.email} - Configuration"


class OTP(models.Model):
    class Meta:
        db_table = 'otp_codes'

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    code = models.CharField(max_length=6)
    purpose = models.CharField(max_length=20)  # registration, password_reset, etc.
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)
    
    def __str__(self):
        return f"{self.user.email} - {self.purpose}"
    
    def is_valid(self):
        return not self.is_used and self.expires_at > timezone.now()
