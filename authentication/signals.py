"""
Django signals for authentication app
"""
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from .utils import setup_new_user
import logging

logger = logging.getLogger(__name__)

User = get_user_model()


@receiver(post_save, sender=User)
def setup_new_user_on_registration(sender, instance, created, **kwargs):
    """
    Auto-setup new users with free package and default configuration
    """
    if created:
        try:
            # Setup new user with default config and free package
            setup_results = setup_new_user(instance)
            
            logger.info(
                f"New user setup completed for {instance.email}: "
                f"config_created={setup_results['config_created']}, "
                f"free_package_assigned={setup_results['free_package_assigned']}"
            )
            
        except Exception as e:
            logger.error(f"Failed to setup new user {instance.email}: {str(e)}")