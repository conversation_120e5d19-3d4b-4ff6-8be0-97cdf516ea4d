"""
Utility functions for authentication app
"""
from payments.models import Package, UserPackage
from .models import UserConfig


def auto_assign_free_package(user):
    """
    Auto-assign free package to a user if available and user doesn't have any package.
    Returns True if package was assigned, False otherwise.
    """
    try:
        # Check if user already has any packages
        if UserPackage.objects.filter(user=user).exists():
            return False
            
        # Find free package
        free_package = Package.objects.filter(
            price=0, 
            is_active=True
        ).first()
        
        if free_package:
            UserPackage.objects.create(
                user=user,
                package=free_package,
                status='active'
            )
            return True
            
    except Exception as e:
        # Log the error but don't fail the registration/login
        print(f"Failed to assign free package to user {user.email}: {str(e)}")
        
    return False


def create_default_user_config(user):
    """
    Create default user configuration if it doesn't exist.
    Returns True if config was created, False if it already existed.
    """
    try:
        if UserConfig.objects.filter(user=user).exists():
            return False
            
        default_config = {
            "auto_approval_each_stage": True,
            "video_preferences": {
                "default_orientation": "landscape",
                "default_duration": 60,
                "auto_publish": False
            },
            "notifications": {
                "email": True,
                "push": False,
                "stage_completion": True,
                "video_ready": True
            },
            "workflow_settings": {
                "require_manual_approval": False,
                "auto_proceed_stages": True,
                "pause_on_errors": True
            }
        }
        
        UserConfig.objects.create(
            user=user,
            config=default_config
        )
        return True
        
    except Exception as e:
        print(f"Failed to create default config for user {user.email}: {str(e)}")
        
    return False


def setup_new_user(user):
    """
    Setup a new user with default configuration and free package if available.
    Returns a dictionary with the results of the setup operations.
    """
    results = {
        'config_created': False,
        'free_package_assigned': False
    }
    
    results['config_created'] = create_default_user_config(user)
    results['free_package_assigned'] = auto_assign_free_package(user)
    
    return results