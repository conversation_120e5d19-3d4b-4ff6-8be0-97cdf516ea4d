from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from payments.models import Package, UserPackage
from authentication.models import UserConfig
from authentication.utils import setup_new_user

User = get_user_model()


class Command(BaseCommand):
    help = 'Test and manage auto-subscription to free packages for users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--check-missing',
            action='store_true',
            help='Check for users without any package and assign free package if available',
        )
        parser.add_argument(
            '--test-user',
            type=str,
            help='Create a test user with the given email and assign free package',
        )
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show statistics about user packages and configurations',
        )

    def handle(self, *args, **options):
        if options['check_missing']:
            self.check_and_assign_missing_packages()
        elif options['test_user']:
            self.create_test_user(options['test_user'])
        elif options['stats']:
            self.show_stats()
        else:
            self.stdout.write(
                self.style.ERROR('Please specify an action: --check-missing, --test-user, or --stats')
            )

    def check_and_assign_missing_packages(self):
        """Check for users without packages and assign free package if available"""
        self.stdout.write("Checking for users without packages...")
        
        # Find users without any packages
        users_without_packages = User.objects.filter(
            user_packages__isnull=True
        ).distinct()
        
        if not users_without_packages.exists():
            self.stdout.write(
                self.style.SUCCESS("✓ All users have packages assigned")
            )
            return
        
        # Check if free package exists
        free_package = Package.objects.filter(price=0, is_active=True).first()
        if not free_package:
            self.stdout.write(
                self.style.WARNING(
                    f"⚠ Found {users_without_packages.count()} users without packages, "
                    "but no free package available"
                )
            )
            return
        
        # Assign free package to users
        assigned_count = 0
        config_created_count = 0
        
        for user in users_without_packages:
            try:
                setup_results = setup_new_user(user)
                if setup_results['free_package_assigned']:
                    assigned_count += 1
                if setup_results['config_created']:
                    config_created_count += 1
                    
                self.stdout.write(f"  ✓ Setup completed for {user.email}")
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"  ✗ Failed to setup {user.email}: {str(e)}")
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f"✓ Assigned free packages to {assigned_count} users, "
                f"created configs for {config_created_count} users"
            )
        )

    def create_test_user(self, email):
        """Create a test user and assign free package"""
        self.stdout.write(f"Creating test user: {email}")
        
        # Check if user already exists
        if User.objects.filter(email=email).exists():
            self.stdout.write(
                self.style.ERROR(f"User with email {email} already exists")
            )
            return
        
        try:
            # Create user
            user = User.objects.create(
                email=email,
                name='Test User',
                is_active=True
            )
            user.set_password('TestPassword123!')
            user.save()
            
            self.stdout.write(f"✓ Created user: {user.email}")
            
            # Check what was auto-assigned via signals
            user_packages = UserPackage.objects.filter(user=user)
            user_config = UserConfig.objects.filter(user=user).first()
            
            if user_packages.exists():
                package = user_packages.first().package
                self.stdout.write(
                    f"✓ Auto-assigned package: {package.name} (${package.price})"
                )
            else:
                self.stdout.write("⚠ No package was auto-assigned")
            
            if user_config:
                self.stdout.write("✓ User configuration created")
            else:
                self.stdout.write("⚠ No user configuration created")
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to create test user: {str(e)}")
            )

    def show_stats(self):
        """Show statistics about users, packages, and configurations"""
        self.stdout.write("User Package Statistics:")
        self.stdout.write("=" * 50)
        
        # Total users
        total_users = User.objects.count()
        self.stdout.write(f"Total users: {total_users}")
        
        # Users with packages
        users_with_packages = User.objects.filter(user_packages__isnull=False).distinct().count()
        self.stdout.write(f"Users with packages: {users_with_packages}")
        
        # Users without packages
        users_without_packages = total_users - users_with_packages
        self.stdout.write(f"Users without packages: {users_without_packages}")
        
        # Users with configurations
        users_with_config = User.objects.filter(userconfig__isnull=False).count()
        self.stdout.write(f"Users with configurations: {users_with_config}")
        
        self.stdout.write("\nPackage Statistics:")
        self.stdout.write("-" * 30)
        
        # Available packages
        packages = Package.objects.filter(is_active=True)
        for package in packages:
            user_count = UserPackage.objects.filter(
                package=package, 
                status='active'
            ).count()
            self.stdout.write(
                f"{package.name} (${package.price}): {user_count} active users"
            )
        
        # Free packages
        free_packages = Package.objects.filter(price=0, is_active=True)
        if free_packages.exists():
            self.stdout.write(f"\nFree packages available: {free_packages.count()}")
            for package in free_packages:
                self.stdout.write(f"  - {package.name}")
        else:
            self.stdout.write("\n⚠ No free packages available")
        
        self.stdout.write("\nRecommendations:")
        self.stdout.write("-" * 30)
        
        if users_without_packages > 0:
            if free_packages.exists():
                self.stdout.write(
                    f"Run with --check-missing to assign free packages to "
                    f"{users_without_packages} users"
                )
            else:
                self.stdout.write(
                    "Consider creating a free package for users without subscriptions"
                )
        
        if total_users - users_with_config > 0:
            self.stdout.write(
                f"Run with --check-missing to create configurations for "
                f"{total_users - users_with_config} users"
            )