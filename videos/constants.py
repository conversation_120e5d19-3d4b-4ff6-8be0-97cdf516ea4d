# Configuration Constants

VIDEO_TYPE_CHOICES = (
    ('faceless', 'Faceless'),
    ('avatar', 'Avatar'),
)

SCRIPT_TYPE_CHOICES = (
    ('from_user_idea', 'From User Idea'),
    ('from_transcript', 'From Transcript'),
)

SPEECH_TYPE_CHOICES = (
    ('tts', 'Text-to-Speech'),
)

VIDEO_STYLE_CHOICES = (
    ('cinematic', 'Cinematic'),
    ('cartoon', 'Cartoon'),
    ('3d-animation', '3D Animation'),
    ('whiteboard', 'Whiteboard'),
    ('motion-graphics', 'Motion Graphics'),
    ('minimalistic', 'Minimalistic'),
    ('photorealistic', 'Photorealistic'),
    ('hyperrealistic', 'Hyperrealistic'),
    ('documentary', 'Documentary'),
    ('corporate', 'Corporate'),
    ('casual', 'Casual'),
    ('social-media', 'Social Media'),
    ('comedy', 'Comedy'),
    ('product-demo', 'Product Demo'),
    ('educational', 'Educational'),
    ('music-video', 'Music Video'),
)

ORIENTATION_CHOICES = (
    ('landscape', 'Landscape'),
    ('portrait', 'Portrait'),
    ('square', 'Square'),
)

IMAGE_PROVIDER_CHOICES = (
    ('flux.1-schnell', 'black-forest-labs/FLUX.1-schnell'),
    ('flux.1-dev', 'black-forest-labs/FLUX.1-dev'),
)

LANGUAGE_CHOICES = (
    ('en', 'English'),
    ('ta', 'Tamil'),
)

VIDEO_STATUS_CHOICES = (
    ('in_queue', 'In Queue'),
    ('in_progress', 'In Progress'),
    ('waiting_for_review', 'Waiting for Review'),
    ('error', 'Error'),
    ('done', 'Done'),
)

VIDEO_STAGE_CHOICES = (
    ('yet_to_start', 'Yet to Start'),
    ('script_generation', 'Script Generation'),
    ('voice_generation', 'Voice Generation'),
    ('caption_generation', 'Caption Generation'),
    ('image_prompt_generation', 'Image Prompt Generation'),
    ('image_generation', 'Image Generation'),
    ('clip_creation', 'Clip Creation'),
    ('track_creation', 'Track Creation'),
    ('video_composition', 'Video Composition'),
    ('completed', 'Completed'),
)

# Stage sequence for video creation flow
VIDEO_CREATION_FLOW = [
    'script_generation',
    'voice_generation',
    'caption_generation',
    'track_creation',
    'clip_creation',
    'image_prompt_generation',
    'image_generation',
    'video_composition'
]

VIDEO_PUBLISH_STATUS_CHOICES = (
    ('pending', 'Pending'),
    ('processing', 'Processing'),
    ('done', 'Done'),
)

TASK_STATUS_CHOICES = (
    ('draft', 'Draft'),
    ('todo', 'Todo'),
    ('inprogress', 'In Progress'),
    ('done', 'Done'),
)

DURATION_CHOICES = (
    (30, '30 seconds'),
    (60, '60 seconds'),
    (90, '90 seconds'),
    (120, '120 seconds'),
)

# Provider Architecture Constants

# Stage to Provider Mapping
STAGE_PROVIDERS = {
    'script_generation': 'n8n',
    'voice_generation': 'n8n', 
    'caption_generation': 'nca',
    'image_prompt_generation': 'n8n',
    'image_generation': 'dynamic',  # Based on video.image_provider
    'clip_creation': 'internal',
    'track_creation': 'internal',
    'video_composition': 'nca',
}

# Dynamic provider mapping for image_generation
IMAGE_PROVIDER_MAPPING = {
    'flux.1-schnell': 'runpod',
    'flux.1-dev': 'runpod',
}

# Dynamically generate stage categorization from STAGE_PROVIDERS
def get_stages_by_provider_type():
    """Generate stage lists based on STAGE_PROVIDERS mapping"""
    external_stages = []
    internal_stages = []
    
    for stage, provider in STAGE_PROVIDERS.items():
        if provider == 'internal':
            internal_stages.append(stage)
        else:
            external_stages.append(stage)
    
    return external_stages, internal_stages

EXTERNAL_STAGES, INTERNAL_STAGES = get_stages_by_provider_type()
