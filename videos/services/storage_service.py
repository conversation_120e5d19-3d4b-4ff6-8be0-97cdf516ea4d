"""
MinIO/S3 Storage Service for uploading and managing media files
"""
import os
import uuid
import logging
import requests
from typing import Optional, Dict, Any
from urllib.parse import urlparse
from io import BytesIO

import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from django.conf import settings

logger = logging.getLogger(__name__)


class StorageService:
    """
    Service for uploading files to MinIO/S3 storage
    """
    
    def __init__(self):
        """Initialize MinIO/S3 client"""
        self.access_key = getattr(settings, 'S3_ACCESS_KEY', '')
        self.secret_key = getattr(settings, 'S3_SECRET_KEY', '')
        self.bucket_name = getattr(settings, 'S3_BUCKET_NAME', 'nca')
        self.endpoint_url = getattr(settings, 'S3_ENDPOINT_URL', 'https://miniio-api.syncu.in')
        self.region = getattr(settings, 'S3_REGION', 'us-east-1')
        self.storage_base_url = getattr(settings, 'STORAGE_BASE_URL', 'https://miniio-api.syncu.in/nca/')
        
        # Initialize S3 client
        self.s3_client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize the S3/MinIO client"""
        try:
            if not self.access_key or not self.secret_key:
                logger.error("MinIO credentials not configured")
                return
            
            self.s3_client = boto3.client(
                's3',
                endpoint_url=self.endpoint_url,
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                region_name=self.region,
                use_ssl=True
            )
            
            # Test connection
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            logger.info(f"MinIO client initialized successfully for bucket: {self.bucket_name}")
            
        except (ClientError, NoCredentialsError) as e:
            logger.error(f"Failed to initialize MinIO client: {str(e)}")
            self.s3_client = None
        except Exception as e:
            logger.error(f"Unexpected error initializing MinIO client: {str(e)}")
            self.s3_client = None
    
    def upload_from_url(self, source_url: str, destination_path: str, 
                       content_type: Optional[str] = None) -> Optional[str]:
        """
        Download file from URL and upload to MinIO
        
        Args:
            source_url: URL to download file from
            destination_path: Path in MinIO bucket (e.g., 'videos/123/image_1.jpg')
            content_type: MIME type of the file (auto-detected if not provided)
            
        Returns:
            str: MinIO URL of uploaded file, or None if failed
        """
        if not self.s3_client:
            logger.error("MinIO client not initialized")
            return None
        
        try:
            # Download file from source URL
            logger.info(f"Downloading file from: {source_url}")
            response = requests.get(source_url, timeout=30)
            response.raise_for_status()
            
            # Auto-detect content type if not provided
            if not content_type:
                content_type = response.headers.get('content-type', 'application/octet-stream')
            
            # Create file-like object from downloaded content
            file_obj = BytesIO(response.content)
            file_size = len(response.content)
            
            # Upload to MinIO
            logger.info(f"Uploading to MinIO: {destination_path} ({file_size} bytes)")
            
            self.s3_client.upload_fileobj(
                file_obj,
                self.bucket_name,
                destination_path,
                ExtraArgs={
                    'ContentType': content_type,
                    'ACL': 'public-read'  # Make file publicly accessible
                }
            )
            
            # Return the MinIO URL (relative path that will be prefixed by STORAGE_BASE_URL)
            return destination_path
            
        except requests.RequestException as e:
            logger.error(f"Failed to download file from {source_url}: {str(e)}")
            return None
        except ClientError as e:
            logger.error(f"Failed to upload to MinIO: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error uploading file: {str(e)}")
            return None
    
    def upload_file(self, file_obj, destination_path: str, 
                   content_type: Optional[str] = None) -> Optional[str]:
        """
        Upload file object directly to MinIO
        
        Args:
            file_obj: File-like object to upload
            destination_path: Path in MinIO bucket
            content_type: MIME type of the file
            
        Returns:
            str: MinIO URL of uploaded file, or None if failed
        """
        if not self.s3_client:
            logger.error("MinIO client not initialized")
            return None
        
        try:
            extra_args = {}
            if content_type:
                extra_args['ContentType'] = content_type
            extra_args['ACL'] = 'public-read'
            
            self.s3_client.upload_fileobj(
                file_obj,
                self.bucket_name,
                destination_path,
                ExtraArgs=extra_args
            )
            
            return destination_path
            
        except ClientError as e:
            logger.error(f"Failed to upload file to MinIO: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error uploading file: {str(e)}")
            return None
    
    def delete_file(self, file_path: str) -> bool:
        """
        Delete file from MinIO
        
        Args:
            file_path: Path of file in MinIO bucket
            
        Returns:
            bool: True if deleted successfully, False otherwise
        """
        if not self.s3_client:
            logger.error("MinIO client not initialized")
            return False
        
        try:
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=file_path)
            logger.info(f"Deleted file from MinIO: {file_path}")
            return True
            
        except ClientError as e:
            logger.error(f"Failed to delete file from MinIO: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error deleting file: {str(e)}")
            return False
    
    def generate_image_path(self, video_id: int, image_index: int, 
                           file_extension: str = 'jpg') -> str:
        """
        Generate a standardized path for image files
        
        Args:
            video_id: ID of the video
            image_index: Index/sequence of the image
            file_extension: File extension (without dot)
            
        Returns:
            str: Generated path for the image
        """
        # Generate unique filename with UUID to avoid conflicts
        unique_id = str(uuid.uuid4())[:8]
        filename = f"image_{image_index}_{unique_id}.{file_extension}"
        return f"videos/{video_id}/images/{filename}"
    
    def is_configured(self) -> bool:
        """
        Check if MinIO storage is properly configured
        
        Returns:
            bool: True if configured and working, False otherwise
        """
        return self.s3_client is not None


# Global instance
storage_service = StorageService()
