"""
NCA Provider for HTTP REST API integration
Handles caption_generation and video_composition stages
"""
import logging
import requests
from typing import Dict, Any, Optional
from django.conf import settings

from videos.models import Video

from .base import BaseServiceProvider, ProviderResponse, ProcessingStatus, ProviderError, CallbackMixin

logger = logging.getLogger(__name__)


class NCAProvider(BaseServiceProvider, CallbackMixin):
    """
    Provider for NCA HTTP REST API integration
    Supports both sync and async response patterns
    """
    
    # Stages supported by NCA
    SUPPORTED_STAGES = [
        'caption_generation',
        'video_composition'
    ]
    
    # NCA API endpoint mappings
    NCA_ENDPOINTS = {
        'caption_generation': '/v1/media/transcribe',
        'video_composition': '/v1/ffmpeg/compose'
    }

    # Callback URL mappings
    CALLBACK_ENDPOINTS = {
        'caption_generation': 'caption_generation',
        'video_composition': 'video_composition',
    }
    
    def __init__(self):
        super().__init__('nca')
        self.base_url = getattr(settings, 'NCA_API_BASE_URL', 'https://nca-toolkit.syncu.in')
        self.api_key = getattr(settings, 'NCA_API_KEY', '')
        self.timeout = getattr(settings, 'NCA_REQUEST_TIMEOUT', 60)
        self.callback_base_url = getattr(settings, 'CALLBACK_BASE_URL', 'https://videoai.syncu.in/api/videos/callback/recieve-response/')

    
    def get_supported_stages(self) -> list:
        """Get list of stages this provider supports"""
        return self.SUPPORTED_STAGES.copy()
    
    def process_stage(
        self, 
        video: Video, 
        stage: str, 
        correlation_id: str, 
        payload: Optional[Dict[str, Any]] = None
    ) -> ProviderResponse:
        """
        Process a stage using NCA HTTP API
        
        Args:
            video: Video model instance
            stage: Stage name
            correlation_id: Correlation ID for tracking
            payload: Optional additional payload data
            
        Returns:
            ProviderResponse: Response with result data
        """
        try:
            self.log_operation('process_stage', video.id, stage, {
                'correlation_id': correlation_id
                
            })
            
            # Validate stage
            if not self.validate_stage(stage):
                raise ProviderError(
                    f"Stage '{stage}' not supported by NCA provider",
                    self.provider_name,
                    stage,
                    'UNSUPPORTED_STAGE'
                )
            
            # Prepare payload for external API (clean, no internal fields)
            api_payload = self.prepare_payload(video, stage)

            # Merge with additional payload if provided
            if payload:
                api_payload.update(payload)

            # Get API endpoint
            api_url = self._get_api_url(stage)

            # Make API request with clean payload
            response_data = self._make_api_request(api_url, api_payload, stage)
            
            # Process response
            result = self.handle_response(response_data, video, stage)
            
            
            return result
            
        except ProviderError:
            raise
        except Exception as e:
            logger.error(f"NCA provider error for stage {stage}: {str(e)}")
            return self.handle_error(e, video, stage)
    
    def prepare_payload(self, video: Video, stage: str) -> Dict[str, Any]:
        """
        Prepare stage-specific payload for NCA API
        
        Args:
            video: Video model instance
            stage: Stage name
            
        Returns:
            Dict: NCA API-specific payload
        """
        # Base payload - don't include internal fields like video_id and stage in API request
        payload = {}

        if stage == 'caption_generation':
            # Validate required fields for caption generation
            if not video.speech_url:
                raise ProviderError(
                    "speech_url is required for caption generation",
                    self.provider_name,
                    stage,
                    'MISSING_SPEECH_URL'
                )


            # Create NCA API payload for transcription
            payload.update({
                'media_url': video.speech_url,  # NCA API expects 'media_url'
                'language': video.language,  # Use language code instead of language name
                'task': 'transcribe',
                'include_text': True,
                'include_srt': True,
                'webhook_url': self._get_webhook_url(stage, video.id),
                'id': f'{video.id}'
            })
        elif stage == 'video_composition':
            payload.update({
                'orientation': video.orientation,
                'duration': video.duration,
                'video_style': video.video_style
            })
            
            # Add BGM if selected
            if video.bgm:
                payload['bgm'] = {
                    'file_path': video.bgm.file_path,
                    'volume': 0.3  # Default BGM volume
                }
            
            # Add tracks and clips data
            tracks_data = self._prepare_tracks_data(video)
            if tracks_data:
                payload['tracks'] = tracks_data
        
        return payload
    
    def handle_response(
        self, 
        response: Any, 
        video: Video, 
        stage: str
    ) -> ProviderResponse:
        """
        Process and standardize NCA API response
        
        Args:
            response: Raw response from NCA API
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized response
        """
        try:
            if isinstance(response, dict):
                # Check for success indicators - NCA API returns message: "success" and code: 200
                is_success = (
                    response.get('message') == 'success' or
                    response.get('status') == 'success' or
                    response.get('success') or
                    response.get('code') == 200 or
                    response.get('code') == 202
                )

                if is_success:
                    # Save job_id in video model for async responses
                    if response.get('async', False) and response.get('job_id'):
                        video.latest_execution_id = response.get('job_id')
                        video.save()
                    return ProviderResponse.success(
                        data=response,
                        execution_id=response.get('job_id'),
                        is_async=response.get('async', False),
                        metadata={'stage': stage, 'provider': 'nca'}
                    )
                else:
                    error_message = response.get('error') or response.get('message', 'NCA processing failed')
                    return ProviderResponse.failure(
                        error_message=error_message,
                        error_code=response.get('error_code', 'NCA_PROCESSING_ERROR'),
                        execution_id=response.get('job_id')
                    )
            else:
                return ProviderResponse.failure(
                    error_message="Invalid response format from NCA API",
                    error_code='INVALID_RESPONSE_FORMAT'
                )
                
        except Exception as e:
            logger.error(f"Error handling NCA response for stage {stage}: {str(e)}")
            return self.handle_error(e, video, stage)
    
    def handle_error(
        self, 
        error: Exception, 
        video: Video, 
        stage: str
    ) -> ProviderResponse:
        """
        Handle NCA provider errors
        
        Args:
            error: Exception that occurred
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized error response
        """
        error_message = str(error)
        error_code = 'NCA_PROVIDER_ERROR'
        
        # Categorize different types of errors
        if isinstance(error, requests.exceptions.Timeout):
            error_code = 'NCA_TIMEOUT_ERROR'
            error_message = f"NCA API timeout for stage {stage}"
        elif isinstance(error, requests.exceptions.ConnectionError):
            error_code = 'NCA_CONNECTION_ERROR'
            error_message = f"Failed to connect to NCA API for stage {stage}"
        elif isinstance(error, requests.exceptions.HTTPError):
            # Check for specific HTTP status codes
            if hasattr(error, 'response') and error.response is not None:
                status_code = error.response.status_code
                if status_code == 401:
                    error_code = 'NCA_AUTHENTICATION_ERROR'
                    error_message = f"NCA API authentication failed for stage {stage}. Check API key configuration."
                elif status_code == 403:
                    error_code = 'NCA_AUTHORIZATION_ERROR'
                    error_message = f"NCA API authorization failed for stage {stage}. API key may not have required permissions."
                elif status_code == 400:
                    error_code = 'NCA_BAD_REQUEST_ERROR'
                    error_message = f"NCA API bad request for stage {stage}. Check payload format: {error_message}"
                elif status_code >= 500:
                    error_code = 'NCA_SERVER_ERROR'
                    error_message = f"NCA API server error for stage {stage}: {error_message}"
                else:
                    error_code = 'NCA_HTTP_ERROR'
                    error_message = f"NCA API HTTP error for stage {stage}: {error_message}"
            else:
                error_code = 'NCA_HTTP_ERROR'
                error_message = f"NCA API HTTP error for stage {stage}: {error_message}"
        elif isinstance(error, requests.exceptions.RequestException):
            error_code = 'NCA_REQUEST_ERROR'
            error_message = f"NCA API request failed for stage {stage}: {error_message}"
        elif isinstance(error, ProviderError):
            error_code = error.error_code or 'NCA_PROVIDER_ERROR'
            error_message = error.message
        
        self.log_operation('handle_error', video.id, stage, {
            'error_code': error_code,
            'error_message': error_message
        })
        
        # Determine if this error should be retried
        
        return ProviderResponse.failure(
            error_message=error_message,
            error_code=error_code,
            metadata={
                'provider': self.provider_name,
                'stage': stage,
                'video_id': video.id,
            
            }
        )

    
    def _get_api_url(self, stage: str) -> str:
        """Get NCA API URL for stage"""
        endpoint = self.NCA_ENDPOINTS.get(stage)
        if not endpoint:
            raise ProviderError(
                f"No NCA endpoint found for stage: {stage}",
                self.provider_name,
                stage,
                'MISSING_ENDPOINT'
            )
        return f"{self.base_url}{endpoint}"
    

    def _get_webhook_url(self, stage: str, video_id) -> str:
        """Get callback webhook URL for stage"""
        callback_endpoint = self.CALLBACK_ENDPOINTS.get(stage)
        if not callback_endpoint:
            raise ProviderError(
                f"No callback endpoint found for stage: {stage}",
                self.provider_name,
                stage,
                'MISSING_CALLBACK_ENDPOINT'
            )
        # Remove trailing slash from base URL to avoid double slashes
        base_url = self.callback_base_url.rstrip('/')
        return f"{base_url}/{video_id}/{callback_endpoint}"
    
    
    def _make_api_request(self, url: str, payload: Dict[str, Any], stage: str) -> dict:
        """
        Make HTTP request to NCA API
        
        Args:
            url: NCA API URL
            payload: Request payload
            stage: Stage name for error context
            
        Returns:
            dict: Response data
            
        Raises:
            requests.RequestException: If request fails
        """
        try:
            logger.info(f"Making NCA API request to {url} for stage {stage}")

            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'AIVIA-VideoAgent/1.0'
            }

            # Add API key for NCA API authentication
            if self.api_key:
                headers['x-api-key'] = f"{self.api_key}"
                logger.debug(f"Using NCA API key: {self.api_key[:8]}...")
            else:
                logger.warning("No NCA API key configured")

            response = requests.post(
                url,
                json=payload,
                timeout=self.timeout,
                headers=headers
            )

            logger.info(f"NCA API response status: {response.status_code}")

            # Log response content for debugging
            if response.status_code != 200 and response.status_code != 202:
                logger.error(f"NCA API error response: {response.text}")

            response.raise_for_status()

            response_data = response.json()
            logger.debug(f"NCA API response data: {response_data}")

            return response_data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"NCA API request failed for stage {stage}: {str(e)}")
            raise

    def _prepare_tracks_data(self, video: Video) -> Optional[list]:
        """
        Prepare tracks and clips data for video composition
        
        Args:
            video: Video model instance
            
        Returns:
            list: Tracks data for NCA API
        """
        try:
            tracks_data = []
            
            for track in video.tracks.all():
                track_data = {
                    'id': track.id,
                    'type': track.type,
                    'layer': track.layer,
                    'clips': []
                }
                
                for clip in track.clips.all():
                    clip_data = {
                        'id': clip.id,
                        'media_url': clip.media.source_path if clip.media else None,
                        'in_point': clip.in_point,
                        'out_point': clip.out_point,
                        'start_time': clip.start_time,
                        'opacity': clip.opacity
                    }
                    
                    # Add volume for audio clips
                    if track.type == 'audio' and clip.volume is not None:
                        clip_data['volume'] = clip.volume
                    
                    track_data['clips'].append(clip_data)
                
                tracks_data.append(track_data)
            
            return tracks_data if tracks_data else None
            
        except Exception as e:
            logger.warning(f"Failed to prepare tracks data: {str(e)}")
            return None
     
    def validate_callback_data(self, data: Dict[str, Any], stage: str) -> bool:
        """
        Validate NCA callback data format
        
        Args:
            data: Callback data
            stage: Stage name
            
        Returns:
            bool: True if data is valid
        """
        required_fields = {
            'caption_generation': ['srt', 'text'],
            'video_composition': ['production_url']
        }
        
        if 'response' not in data:
            return False
        
        stage_fields = required_fields.get(stage, [])
        return all(field in data['response'] for field in stage_fields)

    def process_callback_data(self, video: Video, stage: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process NCA callback data and update video models
        
        Args:
            video: Video model instance
            stage: Stage name
            data: Callback data from NCA
            
        Returns:
            Dict: Processing result
        """
        try:
            if stage == 'caption_generation':
                return self._process_caption_generation(video, data)
            elif stage == 'video_composition':
                return self._process_video_composition(video, data)
            else:
                raise ValueError(f"Unsupported stage: {stage}")
                
        except Exception as e:
            self.logger.error(f"Error processing {stage} callback: {e}")
            raise

    def _process_caption_generation(self, video: Video, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process caption generation callback data"""
        from videos.utils.caption_parser import parse_caption_segments
        
        self.logger.info(f"Caption generation completed for video {video.id}")
        response = data.get('response', {})
        # Store caption data if available
        if 'srt' in response:
            caption_data = response['srt']

            # Store raw caption
            video.caption = caption_data
            
            # Parse and store segments only
            if video.caption:
                segments = parse_caption_segments(video.caption)
                video.parsed_caption = segments
            else:
                video.parsed_caption = []
            
            video.save()
            self.logger.info(f"Stored parsed caption for video {video.id} with {len(video.parsed_caption)} segments")
        else:
            self.logger.warning(f"No caption_data found in response for video {video.id}")
        
        return {
            'message': 'Caption generation data processed and parsed successfully'
        }

    def _process_video_composition(self, video: Video, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process video composition callback data"""
        # Update video with final composition data
        response = data.get('response', {})

        if 'production_url' in response:
            video.production_url = response['production_url']

        if 'raw_url' in response:
            video.raw_url = response['raw_url']

        if 'duration' in response:
            video.duration = response['duration']

        if 'thumbnail_url' in response:
            video.thumbnail_url = response['thumbnail_url']

        video.save()
        self.logger.info(f"Updated composition data for video {video.id}")
        
        return {
            'data_processed': ['production_url', 'raw_url', 'duration', 'thumbnail_url'],
            'message': 'Video composition data processed successfully'
        }

    