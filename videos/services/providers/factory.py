"""
Provider factory for dynamic provider selection and management
"""
import logging
from typing import Dict, Optional
from django.core.exceptions import ImproperlyConfigured

from .base import BaseServiceProvider
from ...constants import STAGE_PROVIDERS, IMAGE_PROVIDER_MAPPING, EXTERNAL_STAGES, INTERNAL_STAGES

logger = logging.getLogger(__name__)


class ProviderFactory:
    """
    Factory class for dynamic provider selection and instantiation
    Handles both static stage-to-provider mapping and dynamic image provider selection
    """
    
    _provider_instances: Dict[str, BaseServiceProvider] = {}
    _initialized = False
    
    @classmethod
    def initialize(cls):
        """Initialize all provider instances"""
        if cls._initialized:
            return
        
        try:
            # Import providers here to avoid circular imports
            from .n8n_provider import N8NProvider
            from .nca_provider import NCAProvider
            from .together_ai_provider import TogetherAIProvider
            from .runpod_provider import RunpodProvider
            
            # Initialize provider instances
            cls._provider_instances = {
                'n8n': N8NProvider(),
                'nca': NCAProvider(),
                'together_ai': TogetherAIProvider(),
                'runpod': RunpodProvider(),
            }
            
            cls._initialized = True
            logger.info("Provider factory initialized with all providers")
            
        except ImportError as e:
            logger.error(f"Failed to import provider: {e}")
            raise ImproperlyConfigured(f"Failed to initialize providers: {e}")
    
    @classmethod
    def get_provider(cls, stage: str, video=None) -> BaseServiceProvider:
        """
        Get the appropriate provider for a given stage
        
        Args:
            stage: Stage name
            video: Video instance (required for dynamic provider selection)
            
        Returns:
            BaseServiceProvider: Provider instance
            
        Raises:
            ValueError: If stage is invalid or provider not found
            ImproperlyConfigured: If providers not properly initialized
        """
        cls.initialize()
        
        if stage not in EXTERNAL_STAGES:
            raise ValueError(f"Stage '{stage}' is not an external stage. Use internal processing.")
        
        if stage == 'image_generation':
            return cls._get_image_provider(video)
        else:
            return cls._get_static_provider(stage)
    
    @classmethod
    def _get_static_provider(cls, stage: str) -> BaseServiceProvider:
        """
        Get provider for stages with static mapping
        
        Args:
            stage: Stage name
            
        Returns:
            BaseServiceProvider: Provider instance
        """
        provider_name = STAGE_PROVIDERS.get(stage)
        if not provider_name:
            raise ValueError(f"No provider mapping found for stage: {stage}")
        
        if provider_name == 'dynamic':
            raise ValueError(f"Stage '{stage}' requires dynamic provider selection")
        
        provider = cls._provider_instances.get(provider_name)
        if not provider:
            raise ValueError(f"Provider '{provider_name}' not found or not initialized")
        
        # Validate provider supports this stage
        if not provider.validate_stage(stage):
            raise ValueError(f"Provider '{provider_name}' does not support stage '{stage}'")
        
        logger.debug(f"Selected provider '{provider_name}' for stage '{stage}'")
        return provider
    
    @classmethod
    def _get_image_provider(cls, video) -> BaseServiceProvider:
        """
        Get image provider based on video's image_provider field
        
        Args:
            video: Video instance with image_provider field
            
        Returns:
            BaseServiceProvider: Image provider instance
        """
        if not video:
            logger.warning("No video provided for image provider selection, using default")
            provider_name = 'together_ai'
        else:
            image_provider = getattr(video, 'image_provider', None) or 'together_ai'
            provider_name = IMAGE_PROVIDER_MAPPING.get(image_provider, 'together_ai')
        
        provider = cls._provider_instances.get(provider_name)
        if not provider:
            logger.warning(f"Image provider '{provider_name}' not found, falling back to together_ai")
            provider = cls._provider_instances.get('together_ai')
        
        if not provider:
            raise ValueError("No image provider available")
        
        # Validate provider supports image generation
        if not provider.validate_stage('image_generation'):
            raise ValueError(f"Provider '{provider_name}' does not support image generation")
        
        logger.debug(f"Selected image provider '{provider_name}' for video {getattr(video, 'id', 'unknown')}")
        return provider
    
    @classmethod
    def get_provider_by_name(cls, provider_name: str) -> Optional[BaseServiceProvider]:
        """
        Get provider instance by name
        
        Args:
            provider_name: Name of the provider
            
        Returns:
            BaseServiceProvider or None: Provider instance if found
        """
        cls.initialize()
        return cls._provider_instances.get(provider_name)
    
    @classmethod
    def list_providers(cls) -> Dict[str, Dict[str, any]]:
        """
        List all available providers and their capabilities
        
        Returns:
            Dict: Provider information
        """
        cls.initialize()
        
        providers_info = {}
        for name, provider in cls._provider_instances.items():
            providers_info[name] = provider.get_provider_info()
        
        return providers_info
    
    @classmethod
    def validate_stage_provider_mapping(cls) -> Dict[str, str]:
        """
        Validate all stage-provider mappings and return status
        
        Returns:
            Dict: Validation results
        """
        cls.initialize()
        
        validation_results = {}
        
        for stage, provider_name in STAGE_PROVIDERS.items():
            if provider_name == 'dynamic':
                validation_results[stage] = "dynamic_mapping_configured"
                continue
            
            if provider_name == 'internal':
                validation_results[stage] = "internal_processing"
                continue
            
            provider = cls._provider_instances.get(provider_name)
            if not provider:
                validation_results[stage] = f"provider_not_found: {provider_name}"
                continue
            
            if provider.validate_stage(stage):
                validation_results[stage] = "valid"
            else:
                validation_results[stage] = f"stage_not_supported_by_provider: {provider_name}"
        
        return validation_results
    
    @classmethod
    def get_available_image_providers(cls) -> list:
        """
        Get list of available image providers
        
        Returns:
            list: List of available image provider names
        """
        cls.initialize()
        
        available_providers = []
        for provider_key in IMAGE_PROVIDER_MAPPING.values():
            provider = cls._provider_instances.get(provider_key)
            if provider and provider.validate_stage('image_generation'):
                available_providers.append(provider_key)
        
        return list(set(available_providers))  # Remove duplicates
    
    @classmethod
    def is_stage_supported(cls, stage: str) -> bool:
        """
        Check if a stage is supported by any provider
        
        Args:
            stage: Stage name
            
        Returns:
            bool: True if stage is supported
        """
        if stage in INTERNAL_STAGES:
            return True
        
        if stage not in STAGE_PROVIDERS:
            return False
        
        provider_name = STAGE_PROVIDERS[stage]
        if provider_name in ['dynamic', 'internal']:
            return True
        
        cls.initialize()
        provider = cls._provider_instances.get(provider_name)
        return provider is not None and provider.validate_stage(stage)
    
    @classmethod
    def reset(cls):
        """Reset factory state (mainly for testing)"""
        cls._provider_instances.clear()
        cls._initialized = False


# Convenience function for external usage
def get_provider_for_stage(stage: str, video=None) -> BaseServiceProvider:
    """
    Convenience function to get provider for a stage
    
    Args:
        stage: Stage name
        video: Video instance (required for dynamic provider selection)
        
    Returns:
        BaseServiceProvider: Provider instance
    """
    return ProviderFactory.get_provider(stage, video)


def validate_provider_setup() -> Dict[str, str]:
    """
    Validate the entire provider setup
    
    Returns:
        Dict: Validation results
    """
    return ProviderFactory.validate_stage_provider_mapping()
