"""
Internal stage processor service - handles internal video creation stages
Keeps all video model logic within the videos module
"""
import logging
from typing import Dict, Any, Optional
from videos.utils.caption_parser import parse_caption_segments

logger = logging.getLogger(__name__)


class InternalStageProcessor:
    """
    Service to handle internal stage processing for video creation
    Processes track_creation and clip_creation stages
    """
    
    def process_track_creation(self, video_id: int) -> Dict[str, Any]:
        """
        Process track creation for a video
        
        Args:
            video_id: Video ID
            
        Returns:
            Dict containing success status and details
        """
        try:
            from videos.models import Video, Track
            
            # Get video instance
            try:
                video = Video.objects.get(id=video_id)
            except Video.DoesNotExist:
                return {
                    'success': False,
                    'error': f"Video {video_id} not found",
                    'error_code': 'VIDEO_NOT_FOUND'
                }
            
            logger.info(f"Creating tracks for video {video.id}")
            
            # Check if tracks already exist to prevent duplicates
            existing_tracks = video.tracks.count()
            if existing_tracks > 0:
                logger.info(f"Tracks already exist for video {video.id} (count: {existing_tracks}). Skipping creation.")
                return {
                    'success': True,
                    'message': f'Tracks already exist (count: {existing_tracks})',
                    'tracks_created': 0
                }
            
            tracks_created = 0
            
            # Create video track (layer 0) - use get_or_create to be extra safe
            video_track, created = Track.objects.get_or_create(
                video=video,
                type='video',
                layer=0
            )
            if created:
                tracks_created += 1
                logger.info(f"Created video track {video_track.id}")
            else:
                logger.info(f"Video track {video_track.id} already exists")
            
            # Create speech audio track (layer 1)
            speech_track, created = Track.objects.get_or_create(
                video=video,
                type='audio',
                layer=1
            )
            if created:
                tracks_created += 1
                logger.info(f"Created speech audio track {speech_track.id}")
            else:
                logger.info(f"Speech audio track {speech_track.id} already exists")
            
            # Create BGM audio track if BGM is selected (layer 2)
            if video.bgm:
                bgm_track, created = Track.objects.get_or_create(
                    video=video,
                    type='audio',
                    layer=2
                )
                if created:
                    tracks_created += 1
                    logger.info(f"Created BGM audio track {bgm_track.id}")
                else:
                    logger.info(f"BGM audio track {bgm_track.id} already exists")
            
            return {
                'success': True,
                'tracks_created': tracks_created,
                'total_tracks': video.tracks.count(),
                'message': f'Successfully processed track creation for video {video.id}'
            }
            
        except Exception as e:
            logger.error(f"Failed to create tracks for video {video_id}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'error_code': 'TRACK_CREATION_FAILED'
            }
    
    def process_clip_creation(self, video_id: int) -> Dict[str, Any]:
        """
        Process clip creation for a video based on caption segments
        
        Args:
            video_id: Video ID
            
        Returns:
            Dict containing success status and details
        """
        try:
            from videos.models import Video, Clip, MediaAsset, MediaGeneration
            
            # Get video instance
            try:
                video = Video.objects.get(id=video_id)
            except Video.DoesNotExist:
                return {
                    'success': False,
                    'error': f"Video {video_id} not found",
                    'error_code': 'VIDEO_NOT_FOUND'
                }
            
            logger.info(f"Creating clips for video {video.id}")
            
            # Check if clips already exist to prevent duplicates
            existing_clips = Clip.objects.filter(track__video=video).count()
            if existing_clips > 0:
                logger.info(f"Clips already exist for video {video.id} (count: {existing_clips}). Skipping creation.")
                return {
                    'success': True,
                    'message': f'Clips already exist (count: {existing_clips})',
                    'clips_created': 0
                }
            
            # Get video and audio tracks
            tracks = video.tracks.all()
            video_track = tracks.filter(type='video').first()
            speech_track = tracks.filter(type='audio', layer=1).first()
            bgm_track = tracks.filter(type='audio', layer=2).first() if video.bgm else None
            
            if not video_track or not speech_track:
                return {
                    'success': False,
                    'error': f"Required tracks missing for video {video.id}",
                    'error_code': 'MISSING_TRACKS'
                }
            
            # Check if captions exist
            if not video.caption:
                return {
                    'success': False,
                    'error': f"No captions found for video {video.id}",
                    'error_code': 'NO_CAPTIONS'
                }
            
            clips_created = 0
            
            # Get caption segments from parsed_caption or parse raw caption
            caption_segments = []
            if video.parsed_caption and isinstance(video.parsed_caption, list):
                caption_segments = video.parsed_caption
                logger.info(f"Using parsed caption segments for video {video.id}")
            elif video.caption:
                caption_segments = parse_caption_segments(video.caption)
                logger.info(f"Parsing raw caption for video {video.id}")
            else:
                return {
                    'success': False,
                    'error': f"No captions found for video {video.id}",
                    'error_code': 'NO_CAPTIONS'
                }
            
            # Create MediaGeneration and MediaAsset for each caption segment
            for i, segment in enumerate(caption_segments, 1):
                # Create MediaGeneration for each segment
                media_gen, created = MediaGeneration.objects.get_or_create(
                    video=video,
                    prompt_sequence=i,
                    defaults={
                        'prompt': "",  # Will be filled in image_prompt_generation stage
                        'input_prompt': segment.get('text', ''),
                        'media_type': 'image',
                        'media_provider': video.image_provider or 'default',
                    }
                )
                if created:
                    logger.info(f"Created MediaGeneration {media_gen.id} for segment {i}")
                
                # Create MediaAsset with empty source_path
                media_asset, created = MediaAsset.objects.get_or_create(
                    generation=media_gen,
                    video=video,
                    sequence_order=i,
                    defaults={
                        'source_path': "",  # Empty initially, will be filled after image generation
                        'type': 'image',
                    }
                )
                if created:
                    logger.info(f"Created MediaAsset {media_asset.id} for segment {i}")
                
                # Create clip for video track referencing the media asset
                video_clip, created = Clip.objects.get_or_create(
                    track=video_track,
                    sequence=i,
                    defaults={
                        'media': media_asset,
                        'start_time': segment.get('start_time', 0),
                        'out_point': segment.get('end_time', 0),
                        'in_point': 0.0,
                    }
                )
                if created:
                    clips_created += 1
                    logger.info(f"Created video clip {video_clip.id} for segment {i}")
            
            # Create single speech clip spanning entire video
            speech_clip, created = Clip.objects.get_or_create(
                track=speech_track,
                sequence=1,  # Single speech clip
                defaults={
                    'start_time': 0,
                    'out_point': video.duration or 30.0,
                    'in_point': 0.0,
                }
            )
            if created:
                clips_created += 1
                logger.info(f"Created speech clip {speech_clip.id} spanning entire video")
            
            # Create BGM clip if BGM track exists (spans entire video)
            if bgm_track and video.bgm:
                bgm_clip, created = Clip.objects.get_or_create(
                    track=bgm_track,
                    sequence=1,  # Single BGM clip
                    defaults={
                        'start_time': 0,
                        'out_point': video.duration or 30.0,
                        'in_point': 0.0,
                    }
                )
                if created:
                    clips_created += 1
                    logger.info(f"Created BGM clip {bgm_clip.id} spanning entire video")
            
            return {
                'success': True,
                'clips_created': clips_created,
                'total_clips': Clip.objects.filter(track__video=video).count(),
                'message': f'Successfully processed clip creation for video {video.id}'
            }
            
        except Exception as e:
            logger.error(f"Failed to create clips for video {video_id}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'error_code': 'CLIP_CREATION_FAILED'
            }

# Global service instance
internal_stage_processor = InternalStageProcessor()