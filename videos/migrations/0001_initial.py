# Generated by Django 5.2.1 on 2025-09-04 07:45

import django.contrib.postgres.fields
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BGM',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('display_name', models.CharField(max_length=200)),
                ('_file_path', models.CharField(db_column='file_path', max_length=500)),
                ('duration', models.IntegerField(help_text='Duration in seconds')),
                ('genre', models.CharField(blank=True, max_length=100, null=True)),
                ('mood', models.CharField(blank=True, max_length=100, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'bgms',
            },
        ),
        migrations.CreateModel(
            name='MediaGeneration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prompt', models.TextField()),
                ('input_prompt', models.TextField(blank=True, help_text='Input text/segment for prompt generation', null=True)),
                ('media_type', models.CharField(max_length=50)),
                ('media_provider', models.CharField(max_length=50)),
                ('prompt_sequence', models.IntegerField(default=0, help_text='Order of this prompt in the sequence')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'media_generations',
            },
        ),
        migrations.CreateModel(
            name='Track',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('video', 'Video'), ('audio', 'Audio')], max_length=10)),
                ('layer', models.IntegerField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'tracks',
            },
        ),
        migrations.CreateModel(
            name='MediaAsset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_source_path', models.URLField(db_column='source_path')),
                ('type', models.CharField(choices=[('video', 'Video'), ('image', 'Image'), ('audio', 'Audio')], max_length=10)),
                ('duration', models.IntegerField(default=0)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('sequence_order', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('generation', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='media_assets', to='videos.mediageneration')),
            ],
            options={
                'db_table': 'media_assets',
                'ordering': ['sequence_order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='Clip',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sequence', models.IntegerField(default=0)),
                ('in_point', models.FloatField(default=0.0)),
                ('out_point', models.FloatField(default=0.0)),
                ('start_time', models.FloatField(default=0.0)),
                ('opacity', models.FloatField(default=1.0)),
                ('volume', models.FloatField(blank=True, default=1.0, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('media', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='clips', to='videos.mediaasset')),
                ('track', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='clips', to='videos.track')),
            ],
            options={
                'db_table': 'clips',
            },
        ),
        migrations.CreateModel(
            name='TTSVoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider_name', models.CharField(max_length=100)),
                ('provider_display_name', models.CharField(max_length=100)),
                ('name', models.CharField(max_length=100)),
                ('display_name', models.CharField(max_length=100)),
                ('_sample_file', models.URLField(blank=True, db_column='sample_file', help_text='URL to a sample audio file for this voice', null=True)),
                ('language', models.CharField(choices=[('en', 'English'), ('ta', 'Tamil')], default='en', max_length=50)),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female'), ('neutral', 'Neutral')], default='neutral', max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'unique_together': {('provider_name', 'name')},
            },
        ),
        migrations.CreateModel(
            name='Video',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('video_type', models.CharField(choices=[('faceless', 'Faceless'), ('avatar', 'Avatar')], max_length=20)),
                ('speech_type', models.CharField(choices=[('tts', 'Text-to-Speech')], max_length=20)),
                ('title', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('caption', models.TextField(blank=True, help_text='Video captions/subtitles', null=True)),
                ('parsed_caption', models.JSONField(blank=True, help_text='Parsed caption segments with timing information', null=True)),
                ('script', models.TextField(blank=True, null=True)),
                ('script_type', models.CharField(choices=[('from_user_idea', 'From User Idea'), ('from_transcript', 'From Transcript')], max_length=20)),
                ('_production_url', models.URLField(blank=True, db_column='production_url', null=True)),
                ('_raw_url', models.URLField(blank=True, db_column='raw_url', null=True)),
                ('_avatar_url', models.URLField(blank=True, db_column='avatar_url', null=True)),
                ('_speech_url', models.URLField(blank=True, db_column='speech_url', null=True)),
                ('video_style', models.CharField(default='default_style', max_length=100)),
                ('context', models.TextField(default='')),
                ('image_provider', models.CharField(blank=True, choices=[('flux.1-schnell', 'black-forest-labs/FLUX.1-schnell'), ('flux.1-dev', 'black-forest-labs/FLUX.1-dev')], max_length=50, null=True)),
                ('video_provider', models.CharField(blank=True, max_length=50, null=True)),
                ('language', models.CharField(choices=[('en', 'English'), ('ta', 'Tamil')], default='en', max_length=50)),
                ('orientation', models.CharField(choices=[('landscape', 'Landscape'), ('portrait', 'Portrait'), ('square', 'Square')], default='landscape', max_length=20)),
                ('platforms', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=50), blank=True, default=list, size=None)),
                ('duration', models.IntegerField(default=0)),
                ('auto_approval_each_stage', models.BooleanField(default=True, help_text='Automatically proceed to next stage without manual approval')),
                ('status', models.CharField(choices=[('in_queue', 'In Queue'), ('in_progress', 'In Progress'), ('waiting_for_review', 'Waiting for Review'), ('error', 'Error'), ('done', 'Done')], max_length=20, null=True)),
                ('stage', models.CharField(choices=[('yet_to_start', 'Yet to Start'), ('script_generation', 'Script Generation'), ('voice_generation', 'Voice Generation'), ('caption_generation', 'Caption Generation'), ('image_prompt_generation', 'Image Prompt Generation'), ('image_generation', 'Image Generation'), ('clip_creation', 'Clip Creation'), ('track_creation', 'Track Creation'), ('video_composition', 'Video Composition'), ('completed', 'Completed')], default='yet_to_start', max_length=30)),
                ('latest_execution_id', models.CharField(blank=True, help_text='Latest N8N execution ID', max_length=255, null=True)),
                ('correlation_id', models.CharField(blank=True, db_index=True, help_text='Correlation ID for tracking workflow stages and callbacks', max_length=255, null=True)),
                ('error', models.TextField(blank=True, null=True)),
                ('publish_status', models.CharField(blank=True, choices=[('pending', 'Pending'), ('processing', 'Processing'), ('done', 'Done')], default='pending', max_length=20, null=True)),
                ('publish_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('account', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='videos', to='accounts.account')),
                ('bgm', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='videos', to='videos.bgm')),
                ('tts_voice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='videos', to='videos.ttsvoice')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='videos', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'videos',
            },
        ),
        migrations.AddField(
            model_name='track',
            name='video',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tracks', to='videos.video'),
        ),
        migrations.AddField(
            model_name='mediageneration',
            name='video',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='media_generations', to='videos.video'),
        ),
        migrations.AddField(
            model_name='mediaasset',
            name='video',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='media_assets', to='videos.video'),
        ),
        migrations.CreateModel(
            name='VideoTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('video_type', models.CharField(choices=[('faceless', 'Faceless'), ('avatar', 'Avatar')], max_length=20)),
                ('context', models.TextField()),
                ('script_type', models.CharField(choices=[('from_user_idea', 'From User Idea'), ('from_transcript', 'From Transcript')], max_length=20)),
                ('speech_type', models.CharField(choices=[('tts', 'Text-to-Speech')], max_length=20)),
                ('video_style', models.CharField(blank=True, max_length=100, null=True)),
                ('image_provider', models.CharField(blank=True, choices=[('flux.1-schnell', 'black-forest-labs/FLUX.1-schnell'), ('flux.1-dev', 'black-forest-labs/FLUX.1-dev')], max_length=50, null=True)),
                ('video_provider', models.CharField(blank=True, max_length=50, null=True)),
                ('orientation', models.CharField(choices=[('landscape', 'Landscape'), ('portrait', 'Portrait'), ('square', 'Square')], default='landscape', max_length=20)),
                ('_real_video_url', models.URLField(blank=True, db_column='real_video_url', null=True)),
                ('_avatar_voice_url', models.URLField(blank=True, db_column='avatar_voice_url', null=True)),
                ('duration', models.IntegerField(choices=[(30, '30 seconds'), (60, '60 seconds'), (90, '90 seconds'), (120, '120 seconds')], default=30)),
                ('auto_approval_each_stage', models.BooleanField(default=True, help_text='Automatically proceed to next stage without manual approval')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('todo', 'Todo'), ('inprogress', 'In Progress'), ('done', 'Done')], default='draft', max_length=20)),
                ('publish_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('bgm', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='video_tasks', to='videos.bgm')),
                ('tts_voice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='video_tasks', to='videos.ttsvoice')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='video_tasks', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'video_tasks',
            },
        ),
        migrations.AddField(
            model_name='video',
            name='task',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='videos', to='videos.videotask'),
        ),
        migrations.CreateModel(
            name='VideoTaskAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='task_accounts', to='accounts.account')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='task_accounts', to='videos.videotask')),
            ],
            options={
                'db_table': 'video_task_accounts',
            },
        ),
    ]
