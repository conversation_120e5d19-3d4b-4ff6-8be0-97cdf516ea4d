from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    VideoTaskViewSet, VideoViewSet, TrackViewSet,
    MediaGenerationViewSet, MediaAssetViewSet, ClipViewSet,
    ConfigurationViewSet,
    receive_callback_response,
)

router = DefaultRouter(trailing_slash = False)
router.register(r'tasks', VideoTaskViewSet, basename='video-task')
router.register(r'videos', VideoViewSet, basename='video')
router.register(r'tracks', TrackViewSet, basename='track')
router.register(r'media-generations', MediaGenerationViewSet, basename='media-generation')
router.register(r'media-assets', MediaAssetViewSet, basename='media-asset')
router.register(r'clips', ClipViewSet, basename='clip')
router.register(r'configurations', ConfigurationViewSet, basename='configuration')

urlpatterns = [
    path('', include(router.urls)),    
    path('callback/recieve-response/<str:video_id>/<str:stage>', receive_callback_response, name='callback-receive-response'),
]