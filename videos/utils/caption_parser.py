"""
Caption parsing utilities for video processing
"""
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)


def parse_caption_segments(captions: str) -> List[Dict[str, Any]]:
    """
    Parse caption text into segments with timing information
    
    Args:
        captions: Caption text in SRT format
        
    Returns:
        List of caption segments with start_time, end_time, and text
    """
    segments = []
    lines = captions.strip().split('\n')
    
    i = 0
    while i < len(lines):
        # Skip empty lines
        if not lines[i].strip():
            i += 1
            continue
        
        # Check if line is a sequence number
        if lines[i].strip().isdigit():
            sequence_num = int(lines[i].strip())
            i += 1
            
            # Next line should be timestamp
            if i < len(lines) and '-->' in lines[i]:
                timestamp_line = lines[i].strip()
                start_str, end_str = timestamp_line.split(' --> ')
                
                start_time = _parse_timestamp(start_str)
                end_time = _parse_timestamp(end_str)
                
                i += 1
                
                # Collect text lines until next sequence or end
                text_lines = []
                while i < len(lines) and not lines[i].strip().isdigit() and lines[i].strip():
                    text_lines.append(lines[i].strip())
                    i += 1
                
                text = ' '.join(text_lines)
                
                segments.append({
                    'sequence': sequence_num,
                    'start_time': start_time,
                    'end_time': end_time,
                    'text': text
                })
            else:
                i += 1
        else:
            i += 1
    
    return segments


def _parse_timestamp(timestamp_str: str) -> float:
    """
    Parse timestamp string to seconds
    
    Args:
        timestamp_str: Timestamp in format HH:MM:SS,mmm
        
    Returns:
        Time in seconds as float
    """
    try:
        # Format: HH:MM:SS,mmm
        time_part, ms_part = timestamp_str.split(',')
        hours, minutes, seconds = map(int, time_part.split(':'))
        milliseconds = int(ms_part)
        
        total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
        return total_seconds
    except Exception:
        return 0.0
