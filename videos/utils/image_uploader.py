"""
Image upload utilities for handling image storage to MinIO
"""
import logging
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse

from videos.services.storage_service import storage_service

logger = logging.getLogger(__name__)


def upload_images_to_minio(video_id: int, image_data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Upload images from external URLs to MinIO and return updated image data
    
    Args:
        video_id: ID of the video these images belong to
        image_data_list: List of image data dictionaries with 'url' or 'image_url' keys
        
    Returns:
        List of updated image data with MinIO URLs
    """
    if not storage_service.is_configured():
        logger.warning("MinIO storage not configured, keeping original URLs")
        return image_data_list
    
    updated_images = []
    
    for index, image_data in enumerate(image_data_list):
        try:
            # Get the original URL from various possible keys
            original_url = (
                image_data.get('url') or 
                image_data.get('image_url') or 
                image_data.get('source_url')
            )
            
            if not original_url:
                logger.warning(f"No URL found in image data at index {index}")
                updated_images.append(image_data)
                continue
            
            # Determine file extension from URL
            parsed_url = urlparse(original_url)
            path_parts = parsed_url.path.split('.')
            file_extension = path_parts[-1] if len(path_parts) > 1 else 'jpg'
            
            # Generate MinIO path
            minio_path = storage_service.generate_image_path(
                video_id=video_id,
                image_index=index + 1,
                file_extension=file_extension
            )
            
            # Upload to MinIO
            logger.info(f"Uploading image {index + 1} to MinIO: {original_url} -> {minio_path}")
            uploaded_path = storage_service.upload_from_url(
                source_url=original_url,
                destination_path=minio_path,
                content_type=f'image/{file_extension}'
            )
            
            if uploaded_path:
                # Update image data with MinIO URL
                updated_image_data = image_data.copy()
                updated_image_data['url'] = uploaded_path
                updated_image_data['image_url'] = uploaded_path
                updated_image_data['original_url'] = original_url
                updated_image_data['storage'] = 'minio'
                
                logger.info(f"Successfully uploaded image {index + 1} to MinIO: {uploaded_path}")
                updated_images.append(updated_image_data)
            else:
                # Keep original URL if upload failed
                logger.error(f"Failed to upload image {index + 1} to MinIO, keeping original URL")
                updated_images.append(image_data)
                
        except Exception as e:
            logger.error(f"Error processing image {index + 1}: {str(e)}")
            updated_images.append(image_data)
    
    return updated_images


def upload_single_image_to_minio(video_id: int, image_url: str, image_index: int = 1) -> Optional[str]:
    """
    Upload a single image from URL to MinIO
    
    Args:
        video_id: ID of the video this image belongs to
        image_url: URL of the image to upload
        image_index: Index/sequence number of the image
        
    Returns:
        str: MinIO path of uploaded image, or None if failed
    """
    if not storage_service.is_configured():
        logger.warning("MinIO storage not configured")
        return None
    
    try:
        # Determine file extension from URL
        parsed_url = urlparse(image_url)
        path_parts = parsed_url.path.split('.')
        file_extension = path_parts[-1] if len(path_parts) > 1 else 'jpg'
        
        # Generate MinIO path
        minio_path = storage_service.generate_image_path(
            video_id=video_id,
            image_index=image_index,
            file_extension=file_extension
        )
        
        # Upload to MinIO
        logger.info(f"Uploading single image to MinIO: {image_url} -> {minio_path}")
        uploaded_path = storage_service.upload_from_url(
            source_url=image_url,
            destination_path=minio_path,
            content_type=f'image/{file_extension}'
        )
        
        if uploaded_path:
            logger.info(f"Successfully uploaded single image to MinIO: {uploaded_path}")
            return uploaded_path
        else:
            logger.error(f"Failed to upload single image to MinIO")
            return None
            
    except Exception as e:
        logger.error(f"Error uploading single image: {str(e)}")
        return None


def get_image_content_type(url: str) -> str:
    """
    Determine content type from image URL
    
    Args:
        url: Image URL
        
    Returns:
        str: Content type (MIME type)
    """
    parsed_url = urlparse(url)
    path_parts = parsed_url.path.lower().split('.')
    
    if len(path_parts) > 1:
        extension = path_parts[-1]
        content_type_map = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'webp': 'image/webp',
            'bmp': 'image/bmp',
            'svg': 'image/svg+xml'
        }
        return content_type_map.get(extension, 'image/jpeg')
    
    return 'image/jpeg'  # Default fallback
