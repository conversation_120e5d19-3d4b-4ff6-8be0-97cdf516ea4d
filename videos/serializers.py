from rest_framework import serializers
from .models import (
    VideoTask, VideoTaskAccount, Video, Track, 
    MediaGeneration, MediaAsset, Clip, TTSVoice
)
from accounts.models import Account
from accounts.serializers import AccountListSerializer

class VideoTaskAccountSerializer(serializers.ModelSerializer):
    account_details = AccountListSerializer(source='account', read_only=True)
    
    class Meta:
        model = VideoTaskAccount
        fields = ('id', 'task', 'account', 'account_details')
        read_only_fields = ('id', 'task')
        
class VideoTaskSerializer(serializers.ModelSerializer):
    accounts = serializers.PrimaryKeyRelatedField(
        queryset=Account.objects.all(),
        many=True, 
        write_only=True,
        required=False
    )
    task_accounts = VideoTaskAccountSerializer(many=True, read_only=True)
    
    # Explicitly include URL fields to ensure they appear in API response
    real_video_url = serializers.URLField(read_only=True)
    avatar_voice_url = serializers.URLField(read_only=True)
    
    class Meta:
        model = VideoTask
        fields = '__all__'
        read_only_fields = ('id', 'user', 'created_at')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # if we're updating (instance exists) make topic optional
        if self.instance is not None:
            self.fields['video_type'].required = False
            self.fields['context'].required = False
            self.fields['script_type'].required = False
            self.fields['speech_type'].required = False
        
    def create(self, validated_data):
        accounts = validated_data.pop('accounts', [])
        user = self.context['request'].user
        
        # Get user's default auto_approval_each_stage setting if not provided
        if 'auto_approval_each_stage' not in validated_data:
            try:
                user_config = user.user_configs.first()
                if user_config and user_config.config.get('auto_approval_each_stage') is not None:
                    validated_data['auto_approval_each_stage'] = user_config.config.get('auto_approval_each_stage', True)
                else:
                    validated_data['auto_approval_each_stage'] = True
            except Exception:
                validated_data['auto_approval_each_stage'] = True
        
        video_task = VideoTask.objects.create(**validated_data)
        
        # Create task_account relationships
        for account in accounts:
            VideoTaskAccount.objects.create(task=video_task, account=account)
            
        return video_task
        
    def update(self, instance, validated_data):
        accounts = validated_data.pop('accounts', None)
        
        # Update task with validated data
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update accounts if provided
        if accounts is not None:
            # Clear existing relationships
            instance.task_accounts.all().delete()
            
            # Create new relationships
            for account in accounts:
                VideoTaskAccount.objects.create(task=instance, account=account)
                
        return instance

class ClipSerializer(serializers.ModelSerializer):
    class Meta:
        model = Clip
        fields = '__all__'
        read_only_fields = ('id', 'created_at')

class TrackSerializer(serializers.ModelSerializer):
    clips = ClipSerializer(many=True, read_only=True)
    
    class Meta:
        model = Track
        fields = '__all__'
        read_only_fields = ('id', 'created_at')

class MediaAssetSerializer(serializers.ModelSerializer):
    # Explicitly include URL field to ensure it appears in API response
    source_path = serializers.URLField(read_only=True)
    
    class Meta:
        model = MediaAsset
        fields = '__all__'
        read_only_fields = ('id', 'created_at')

class MediaGenerationSerializer(serializers.ModelSerializer):
    media_assets = MediaAssetSerializer(many=True, read_only=True)
    
    class Meta:
        model = MediaGeneration
        fields = '__all__'
        read_only_fields = ('id', 'created_at')

class TTSVoiceSerializer(serializers.ModelSerializer):
    # Explicitly include URL field to ensure it appears in API response
    sample_file = serializers.URLField(read_only=True)
    
    class Meta:
        model = TTSVoice
        fields = '__all__'
        read_only_fields = ('id', 'created_at')

class VideoDetailSerializer(serializers.ModelSerializer):
    tracks = TrackSerializer(many=True, read_only=True)
    media_generations = MediaGenerationSerializer(many=True, read_only=True)
    account_details = AccountListSerializer(source='account', read_only=True)
    tts_voice = TTSVoiceSerializer(read_only=True)
    
    # Explicitly include URL fields to ensure they appear in API response
    production_url = serializers.URLField(read_only=True)
    raw_url = serializers.URLField(read_only=True)
    avatar_url = serializers.URLField(read_only=True)
    speech_url = serializers.URLField(read_only=True)
    
    class Meta:
        model = Video
        fields = [field.name for field in Video._meta.fields if not field.name.startswith('_')]
        fields += [
            'tracks', 'media_generations', 'account_details', 'tts_voice', 'speech_url',
            'production_url', 'raw_url', 'avatar_url'
        ]
        read_only_fields = ('id', 'user', 'created_at')
        

class VideoListSerializer(serializers.ModelSerializer):
    account_name = serializers.CharField(source='account.name', read_only=True)
    
    class Meta:
        model = Video
        fields = ('id', 'title', 'video_type', 'account', 'account_name', 'status', 
                  'stage', 'error', 'publish_status', 'duration', 'production_url', 'created_at')
        read_only_fields = ('id', 'created_at')

class VideoCreateUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Video
        fields = '__all__'
        read_only_fields = ('id', 'user', 'created_at')
        
    def create(self, validated_data):
        user = self.context['request'].user
        
        # Get auto_approval_each_stage from task if available, otherwise from user config
        if 'auto_approval_each_stage' not in validated_data:
            task = validated_data.get('task')
            if task and hasattr(task, 'auto_approval_each_stage'):
                validated_data['auto_approval_each_stage'] = task.auto_approval_each_stage
            else:
                # Fallback to user config
                try:
                    user_config = user.user_configs.first()
                    if user_config and user_config.config.get('auto_approval_each_stage') is not None:
                        validated_data['auto_approval_each_stage'] = user_config.config.get('auto_approval_each_stage', True)
                    else:
                        validated_data['auto_approval_each_stage'] = True
                except Exception:
                    validated_data['auto_approval_each_stage'] = True
        
        video = Video.objects.create(user=user, **validated_data)
        return video
        
    def to_representation(self, instance):
        """Ensure URL fields are included in the response"""
        representation = super().to_representation(instance)
        representation['production_url'] = instance.production_url
        representation['raw_url'] = instance.raw_url
        representation['avatar_url'] = instance.avatar_url
        representation['speech_url'] = instance.speech_url
        return representation