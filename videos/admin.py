from django.contrib import admin
from .models import (
    VideoTask, VideoTaskAccount, Video, Track, 
    MediaGeneration, MediaAsset, Clip, TTSVoice, BGM
)

class VideoTaskAccountInline(admin.TabularInline):
    model = VideoTaskAccount
    extra = 1
    raw_id_fields = ('account',)

class VideoTaskAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'video_type', 'speech_type', 'status', 'created_at')
    list_filter = ('status', 'video_type', 'speech_type')
    search_fields = ('id', 'user__email', 'context')
    date_hierarchy = 'created_at'
    inlines = [VideoTaskAccountInline]

class TrackInline(admin.TabularInline):
    model = Track
    extra = 1

class MediaGenerationInline(admin.TabularInline):
    model = MediaGeneration
    extra = 0

class VideoAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'user', 'video_type', 'status', 'stage', 'latest_execution_id', 'publish_status', 'created_at')
    list_filter = ('status', 'stage', 'publish_status', 'video_type', 'speech_type')
    search_fields = ('id', 'title', 'user__email', 'description', 'script', 'error', 'latest_execution_id')
    date_hierarchy = 'created_at'
    inlines = [TrackInline, MediaGenerationInline]
    readonly_fields = ('error', 'latest_execution_id')  # Make error and execution_id fields read-only in admin

class ClipInline(admin.TabularInline):
    model = Clip
    extra = 1
    raw_id_fields = ('media',)

class TrackAdmin(admin.ModelAdmin):
    list_display = ('id', 'video', 'type', 'layer', 'created_at')
    list_filter = ('type',)
    search_fields = ('video__title',)
    inlines = [ClipInline]

class MediaAssetInline(admin.TabularInline):
    model = MediaAsset
    extra = 1

class MediaGenerationAdmin(admin.ModelAdmin):
    list_display = ('id', 'video', 'media_type', 'media_provider', 'created_at')
    list_filter = ('media_type', 'media_provider')
    search_fields = ('video__title', 'prompt')
    inlines = [MediaAssetInline]

class MediaAssetAdmin(admin.ModelAdmin):
    list_display = ('id', 'type', 'source_path', 'duration', 'created_at')
    list_filter = ('type',)
    search_fields = ('source_path',)

class ClipAdmin(admin.ModelAdmin):
    list_display = ('id', 'track', 'start_time', 'in_point', 'out_point', 'created_at')
    search_fields = ('track__video__title',)

admin.site.register(VideoTask, VideoTaskAdmin)
admin.site.register(Video, VideoAdmin)
admin.site.register(Track, TrackAdmin)
admin.site.register(MediaGeneration, MediaGenerationAdmin)
admin.site.register(MediaAsset, MediaAssetAdmin)
admin.site.register(Clip, ClipAdmin)

# Register new models
@admin.register(TTSVoice)
class TTSVoiceAdmin(admin.ModelAdmin):
    list_display = ['display_name', 'provider_display_name', 'language', 'gender', 'is_active']
    list_filter = ['provider_name', 'language', 'gender', 'is_active']
    search_fields = ['display_name', 'provider_display_name']


@admin.register(BGM)
class BGMAdmin(admin.ModelAdmin):
    list_display = ['display_name', 'genre', 'mood', 'duration', 'is_active']
    list_filter = ['genre', 'mood', 'is_active']
    search_fields = ['display_name', 'name']
