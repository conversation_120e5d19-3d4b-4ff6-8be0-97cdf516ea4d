"""
Django management command to test MinIO storage integration
"""

from django.core.management.base import BaseCommand
from videos.services.storage_service import storage_service
from videos.utils.image_uploader import upload_single_image_to_minio


class Command(BaseCommand):
    help = 'Test MinIO storage integration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-upload',
            action='store_true',
            help='Test uploading a sample image to MinIO'
        )
        parser.add_argument(
            '--image-url',
            type=str,
            help='URL of image to test upload (requires --test-upload)'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🧪 Testing MinIO Storage Integration')
        )
        
        # Test configuration
        self.test_configuration()
        
        # Test upload if requested
        if options['test_upload']:
            image_url = options.get('image_url', 'https://httpbin.org/image/jpeg')
            self.test_upload(image_url)

    def test_configuration(self):
        """Test MinIO configuration"""
        self.stdout.write('\n=== Configuration Test ===')
        
        if storage_service.is_configured():
            self.stdout.write(
                self.style.SUCCESS('✅ MinIO is properly configured')
            )
            self.stdout.write(f'   Bucket: {storage_service.bucket_name}')
            self.stdout.write(f'   Endpoint: {storage_service.endpoint_url}')
            self.stdout.write(f'   Storage Base URL: {storage_service.storage_base_url}')
        else:
            self.stdout.write(
                self.style.ERROR('❌ MinIO is not configured properly')
            )
            self.stdout.write('   Check S3_ACCESS_KEY and S3_SECRET_KEY environment variables')

    def test_upload(self, image_url):
        """Test uploading an image"""
        self.stdout.write(f'\n=== Upload Test ===')
        self.stdout.write(f'Testing upload from: {image_url}')
        
        try:
            result = upload_single_image_to_minio(
                video_id=999,  # Test video ID
                image_url=image_url,
                image_index=1
            )
            
            if result:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Upload successful!')
                )
                self.stdout.write(f'   MinIO path: {result}')
                
                # Show full URL
                from videos.models import get_full_url
                class MockObj:
                    def __init__(self, path):
                        self._source_path = path
                        self.__dict__ = {'_source_path': path}
                
                mock_obj = MockObj(result)
                full_url = get_full_url(mock_obj, '_source_path')
                self.stdout.write(f'   Full URL: {full_url}')
            else:
                self.stdout.write(
                    self.style.ERROR('❌ Upload failed')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Upload error: {str(e)}')
            )
