"""
Management command to validate provider setup and configuration
"""
from django.core.management.base import BaseCommand
from django.conf import settings
from videos.services.providers import ProviderFactory, validate_provider_setup
from videos.constants import STAGE_PROVIDERS, IMAGE_PROVIDER_MAPPING


class Command(BaseCommand):
    help = 'Validate provider setup and configuration'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--check-health',
            action='store_true',
            help='Check health of all providers'
        )
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed provider information'
        )
    
    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🔍 Provider Setup Validation')
        )
        self.stdout.write('=' * 50)
        
        # Validate stage-provider mappings
        self.stdout.write('\n📋 Stage-Provider Mappings:')
        validation_results = validate_provider_setup()
        
        for stage, status in validation_results.items():
            if status == 'valid':
                self.stdout.write(f"  ✅ {stage}: {status}")
            elif status.startswith('dynamic_mapping') or status.startswith('internal_processing'):
                self.stdout.write(f"  🔄 {stage}: {status}")
            else:
                self.stdout.write(
                    self.style.ERROR(f"  ❌ {stage}: {status}")
                )
        
        # Show provider information
        if options['detailed']:
            self.stdout.write('\n📦 Available Providers:')
            try:
                providers_info = ProviderFactory.list_providers()
                for name, info in providers_info.items():
                    self.stdout.write(f"\n  🔧 {name}:")
                    self.stdout.write(f"     Type: {info.get('type', 'unknown')}")
                    self.stdout.write(f"     Stages: {', '.join(info.get('supported_stages', []))}")
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"  ❌ Failed to get provider info: {str(e)}")
                )
        
        # Show image provider mappings
        self.stdout.write('\n🖼️  Image Provider Mappings:')
        for image_provider, actual_provider in IMAGE_PROVIDER_MAPPING.items():
            try:
                provider = ProviderFactory.get_provider_by_name(actual_provider)
                status = "✅ Available" if provider else "❌ Not Available"
                self.stdout.write(f"  {image_provider} → {actual_provider}: {status}")
            except Exception as e:
                self.stdout.write(f"  {image_provider} → {actual_provider}: ❌ Error - {str(e)}")
        
        # Check health if requested
        if options['check_health']:
            self.stdout.write('\n🏥 Provider Health Check:')
            self._check_provider_health()
        
        # Show configuration status
        self.stdout.write('\n⚙️  Configuration Status:')
        self._check_configuration()
        
        self.stdout.write('\n' + '=' * 50)
        self.stdout.write(
            self.style.SUCCESS('✅ Provider validation completed')
        )
    
    def _check_provider_health(self):
        """Check health of all providers"""
        try:
            # N8N Provider
            n8n_base_url = getattr(settings, 'N8N_WEBHOOK_BASE_URL', None)
            if n8n_base_url:
                self.stdout.write("  🔄 N8N: Configuration found")
            else:
                self.stdout.write("  ❌ N8N: No base URL configured")
            
            # NCA Provider
            nca_base_url = getattr(settings, 'NCA_API_BASE_URL', None)
            nca_api_key = getattr(settings, 'NCA_API_KEY', None)
            if nca_base_url and nca_api_key:
                self.stdout.write("  ✅ NCA: Fully configured")
            elif nca_base_url:
                self.stdout.write("  ⚠️  NCA: Base URL configured, but missing API key")
            else:
                self.stdout.write("  ❌ NCA: Not configured")
            
            # Together.AI Provider
            together_api_key = getattr(settings, 'TOGETHER_AI_API_KEY', None)
            if together_api_key:
                self.stdout.write("  ✅ Together.AI: API key configured")
            else:
                self.stdout.write("  ❌ Together.AI: No API key configured")
            
            # Runpod Provider
            runpod_api_key = getattr(settings, 'RUNPOD_API_KEY', None)
            runpod_endpoint = getattr(settings, 'RUNPOD_ENDPOINT_ID', None)
            if runpod_api_key and runpod_endpoint:
                self.stdout.write("  ✅ Runpod: Fully configured")
            elif runpod_api_key:
                self.stdout.write("  ⚠️  Runpod: API key configured, but missing endpoint ID")
            else:
                self.stdout.write("  ❌ Runpod: Not configured")
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"  ❌ Health check failed: {str(e)}")
            )
    
    def _check_configuration(self):
        """Check overall configuration status"""
        config_issues = []
        
        # Check required settings
        required_settings = [
            'N8N_WEBHOOK_BASE_URL',
            'N8N_CREATE_VIDEO_API'
        ]
        
        for setting in required_settings:
            if not getattr(settings, setting, None):
                config_issues.append(f"Missing setting: {setting}")
        
        # Check provider-specific settings
        provider_settings = {
            'NCA': ['NCA_API_BASE_URL'],
            'Together.AI': ['TOGETHER_AI_API_KEY'],
            'Runpod': ['RUNPOD_API_KEY', 'RUNPOD_ENDPOINT_ID']
        }
        
        for provider, settings_list in provider_settings.items():
            missing = [s for s in settings_list if not getattr(settings, s, None)]
            if missing:
                self.stdout.write(f"  ⚠️  {provider}: Missing {', '.join(missing)}")
            else:
                self.stdout.write(f"  ✅ {provider}: All settings configured")
        
        if config_issues:
            self.stdout.write("\n  Configuration Issues:")
            for issue in config_issues:
                self.stdout.write(f"    ❌ {issue}")
        else:
            self.stdout.write("  ✅ Core configuration looks good")
