from django.core.management.base import BaseCommand
from django.utils import timezone
from videos.models import Video
from videos.constants import VIDEO_CREATION_FLOW


class Command(BaseCommand):
    help = 'Monitor video creation progress and show status'

    def add_arguments(self, parser):
        parser.add_argument(
            '--video-id',
            type=int,
            help='Show status for a specific video ID',
        )
        parser.add_argument(
            '--stuck',
            action='store_true',
            help='Show videos that might be stuck in processing',
        )
        parser.add_argument(
            '--errors',
            action='store_true',
            help='Show videos with errors',
        )

    def handle(self, *args, **options):
        if options['video_id']:
            self.show_video_status(options['video_id'])
        elif options['stuck']:
            self.show_stuck_videos()
        elif options['errors']:
            self.show_error_videos()
        else:
            self.show_all_processing_videos()

    def show_video_status(self, video_id):
        """Show detailed status for a specific video"""
        try:
            video = Video.objects.get(id=video_id)
            self.stdout.write(f"\n=== Video {video_id} Status ===")
            self.stdout.write(f"Title: {video.title}")
            self.stdout.write(f"User: {video.user.email}")
            self.stdout.write(f"Status: {video.status}")
            self.stdout.write(f"Stage: {video.stage}")
            self.stdout.write(f"Execution ID: {video.latest_execution_id}")
            self.stdout.write(f"Created: {video.created_at}")
            
            if video.error:
                self.stdout.write(
                    self.style.ERROR(f"Error: {video.error}")
                )
            
            # Show progress
            if video.stage in VIDEO_CREATION_FLOW:
                current_index = VIDEO_CREATION_FLOW.index(video.stage)
                total_stages = len(VIDEO_CREATION_FLOW)
                progress = (current_index + 1) / total_stages * 100
                self.stdout.write(f"Progress: {progress:.1f}% ({current_index + 1}/{total_stages})")
            
        except Video.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Video {video_id} not found")
            )

    def show_stuck_videos(self):
        """Show videos that might be stuck in processing"""
        # Videos in progress for more than 30 minutes
        cutoff_time = timezone.now() - timezone.timedelta(minutes=30)
        
        stuck_videos = Video.objects.filter(
            status='in_progress',
            created_at__lt=cutoff_time
        ).order_by('created_at')
        
        self.stdout.write(f"\n=== Potentially Stuck Videos ===")
        
        if not stuck_videos:
            self.stdout.write("No stuck videos found.")
            return
        
        for video in stuck_videos:
            age = timezone.now() - video.created_at
            self.stdout.write(
                f"Video {video.id} - {video.title} - Stage: {video.stage} - "
                f"Age: {age.total_seconds()/3600:.1f}h - "
                f"Execution: {video.latest_execution_id}"
            )

    def show_error_videos(self):
        """Show videos with errors"""
        error_videos = Video.objects.filter(
            status='error'
        ).order_by('-created_at')[:10]
        
        self.stdout.write(f"\n=== Videos with Errors (Last 10) ===")
        
        if not error_videos:
            self.stdout.write("No error videos found.")
            return
        
        for video in error_videos:
            self.stdout.write(
                f"Video {video.id} - {video.title} - Stage: {video.stage}"
            )
            self.stdout.write(
                self.style.ERROR(f"  Error: {video.error}")
            )

    def show_all_processing_videos(self):
        """Show all videos currently in processing"""
        processing_videos = Video.objects.filter(
            status__in=['in_queue', 'in_progress']
        ).order_by('created_at')
        
        self.stdout.write(f"\n=== Videos in Processing ===")
        
        if not processing_videos:
            self.stdout.write("No videos currently in processing.")
            return
        
        for video in processing_videos:
            age = timezone.now() - video.created_at
            self.stdout.write(
                f"Video {video.id} - {video.title} - "
                f"Status: {video.status} - Stage: {video.stage} - "
                f"Age: {age.total_seconds()/60:.1f}m"
            )
