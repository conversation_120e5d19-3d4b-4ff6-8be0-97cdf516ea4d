"""
Management command to set up initial data for BGMs and TTS Voices
"""
from django.core.management.base import BaseCommand
from videos.models import BGM, TTSVoice


class Command(BaseCommand):
    help = 'Set up initial data for BGMs and TTS Voices tables'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all existing BGMs and TTS Voices data'
        )
    
    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('🔄 Resetting existing BGMs and TTS Voices...')
            BGM.objects.all().delete()
            TTSVoice.objects.all().delete()
        
        self.stdout.write('🚀 Setting up initial BGMs and TTS Voices data...')
        
        # Set up initial BGM data
        self.setup_bgm_data()
        
        # Set up initial TTS Voice data
        self.setup_tts_voice_data()
        
        self.stdout.write('\n✅ Successfully set up initial data!')
        self.show_summary()
    
    def setup_bgm_data(self):
        """Set up initial BGM data"""
        self.stdout.write('\n🎵 Setting up BGM data...')
        
        # Multiple BGM options for better variety
        bgm_list = [
            {
                'name': 'aayirathil_oruvan',
                'display_name': 'Aayirathil Oruvan',
                '_file_path': 'music/aayirathil_oruvan.mp3',
                'duration': 180,  # 3 minutes
                'genre': 'Devotional',
                'mood': 'Upbeat',
                'is_active': True
            }
        ]
        
        created_count = 0
        for bgm_data in bgm_list:
            bgm, created = BGM.objects.get_or_create(
                name=bgm_data['name'],
                defaults=bgm_data
            )
            
            if created:
                self.stdout.write(f'  ✅ Created BGM: {bgm.display_name}')
                created_count += 1
            else:
                self.stdout.write(f'  ⚠️  BGM already exists: {bgm.display_name}')
        
        self.stdout.write(f'  📊 Created {created_count} new BGM(s)')
        return created_count
    
    def setup_tts_voice_data(self):
        """Set up initial TTS Voice data"""
        self.stdout.write('\n🗣️  Setting up TTS Voice data...')
        
        # Multiple TTS voice options from different providers
        tts_voices_list = [
            {
                'provider_name': 'elevenlabs',
                'provider_display_name': 'ElevenLabs',
                'name': 'dPah2VEoifKnZT37774q',
                'display_name': 'KnoxDart2',
                'sample_file': '',
                'language': 'en',
                'gender': 'male',
                'is_active': True
            },
            {
                'provider_name': 'elevenlabs',
                'provider_display_name': 'ElevenLabs',
                'name': 'jsxww9ngE2fwXHlkWgkm',
                'display_name': 'Rama',
                'sample_file': '',
                'language': 'ta',
                'gender': 'male',
                'is_active': True
            }
        ]
        
        created_count = 0
        for tts_voice_data in tts_voices_list:
            tts_voice, created = TTSVoice.objects.get_or_create(
                provider_name=tts_voice_data['provider_name'],
                name=tts_voice_data['name'],
                defaults=tts_voice_data
            )
            
            if created:
                self.stdout.write(f'  ✅ Created TTS Voice: {tts_voice.display_name}')
                created_count += 1
            else:
                self.stdout.write(f'  ⚠️  TTS Voice already exists: {tts_voice.display_name}')
        
        self.stdout.write(f'  📊 Created {created_count} new TTS Voice(s)')
        return created_count
    
    def show_summary(self):
        """Show summary of current data"""
        self.stdout.write('\n📊 Current Data Summary:')
        
        # BGM Summary
        bgm_count = BGM.objects.filter(is_active=True).count()
        self.stdout.write(f'  🎵 Active BGMs: {bgm_count}')
        
        for bgm in BGM.objects.filter(is_active=True):
            self.stdout.write(f'    • {bgm.display_name} ({bgm.genre}, {bgm.mood}) - {bgm.duration}s')
        
        # TTS Voice Summary
        tts_voice_count = TTSVoice.objects.filter(is_active=True).count()
        self.stdout.write(f'  🗣️  Active TTS Voices: {tts_voice_count}')
        
        for voice in TTSVoice.objects.filter(is_active=True):
            self.stdout.write(f'    • {voice.display_name} ({voice.provider_display_name}, {voice.language}, {voice.gender})')
        
        self.stdout.write('\n🎯 Initial data setup complete!')
        self.stdout.write('   Use these commands to manage data:')
        self.stdout.write('   • python manage.py setup_initial_data --reset  (reset and recreate)')
        self.stdout.write('   • Access Django admin to add more BGMs and voices')
