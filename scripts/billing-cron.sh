#!/bin/bash

# Billing cycle reset cron script
# Runs daily at 2 AM to reset completed billing cycles

# Navigate to app directory
cd /app

# Set up environment
export PYTHONPATH=/app
export DJANGO_SETTINGS_MODULE=videoagent.settings

# Create log directory if it doesn't exist
mkdir -p /app/logs

# Function to log with timestamp
log_with_timestamp() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> /app/logs/billing_reset.log
}

# Start cron job
log_with_timestamp "Starting billing cycle reset process"

# Run the command with error handling
if python manage.py reset_monthly_usage >> /app/logs/billing_reset.log 2>&1; then
    log_with_timestamp "Billing cycle reset completed successfully"
else
    log_with_timestamp "ERROR: Billing cycle reset failed with exit code $?"
fi

log_with_timestamp "Billing cycle reset process finished"
echo "---" >> /app/logs/billing_reset.log