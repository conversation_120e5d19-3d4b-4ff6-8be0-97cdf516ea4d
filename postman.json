{"info": {"name": "AIVIA VideoAgent API - Updated", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "Comprehensive Postman collection for AIVIA Video Automation Backend API. Includes:\n\n- Authentication & User Management\n- Video Task Management & Creation Flow\n- Complete Payment System with Multi-Gateway Support\n- Media Management (Tracks, Media Assets, Clips, Generations)\n- Video Retry System with All Stages\n- Provider Callback Endpoints\n- Usage Analytics & Subscription Management\n- Webhook Integration\n\nVersion 7.0 Updates:\n- Added Media Management endpoints (tracks, media-generations, media-assets, clips)\n- Updated callback URLs to correct format (video_id/stage)\n- Removed non-existent test payment endpoints\n- Added payment session status endpoint\n- Complete retry system for all video creation stages\n- Enhanced callback endpoints for all video creation stages", "version": "7.0.0"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "description": "Base URL for the API"}, {"key": "token", "value": "", "description": "Authentication token - set after login"}, {"key": "active_video_id", "value": "1", "description": "Active video ID for testing - update with actual video ID"}, {"key": "manual_video_id", "value": "", "description": "Manual approval video ID - automatically set when creating manual approval videos"}, {"key": "draft_task_id", "value": "", "description": "Draft task ID - automatically set when saving drafts"}, {"key": "production_task_id", "value": "", "description": "Production task ID - automatically set when proceeding with tasks"}, {"key": "payment_session_id", "value": "", "description": "Payment session ID - automatically set when creating payment sessions"}, {"key": "gateway_session_id", "value": "", "description": "Gateway session ID - automatically set from payment responses"}], "item": [{"name": "Authentication", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/register", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"name\": \"Test User\",\n  \"password\": \"Pass@1234\",\n  \"confirm_password\": \"Pass@1234\",\n  \"mobile\": \"1234567890\"\n}"}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/verify-otp", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\"\n}"}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.token) {", "    pm.environment.set('token', json.token);", "}"]}}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/login", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Pass@1234\"\n}"}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "pm.environment.set('token', json.token);"]}}]}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/profile"}}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/change-password", "body": {"mode": "raw", "raw": "{\n  \"old_password\": \"Pass@1234\",\n  \"new_password\": \"NewPass@1234\",\n  \"confirm_password\": \"NewPass@1234\"\n}"}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/logout"}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/forgot-password", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/reset-password", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\",\n  \"new_password\": \"NewPass@1234\",\n  \"confirm_password\": \"NewPass@1234\"\n}"}}}]}, {"name": "User Configuration", "description": "User configuration management with JSON storage", "item": [{"name": "Get User Configuration", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "description": "Retrieve current user configuration. Returns 404 if no configuration exists."}}, {"name": "Save User Configuration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "body": {"mode": "raw", "raw": "{\n  \"config\": {\n    \"theme\": \"dark\",\n    \"language\": \"en\",\n    \"video_preferences\": {\n      \"default_orientation\": \"landscape\",\n      \"default_duration\": 60,\n      \"auto_publish\": false\n    },\n    \"notifications\": {\n      \"email\": true,\n      \"push\": false,\n      \"completion_alerts\": true\n    }\n  }\n}"}}}, {"name": "Update Configuration", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "body": {"mode": "raw", "raw": "{\n  \"config\": {\n    \"theme\": \"light\",\n    \"notifications\": {\n      \"email\": false\n    }\n  }\n}"}}}, {"name": "Delete Configuration", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config"}}]}, {"name": "Accounts", "item": [{"name": "List Accounts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts"}}, {"name": "Create Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts", "body": {"mode": "raw", "raw": "{\n  \"name\": \"MyC<PERSON><PERSON>\",\n  \"topic\": \"Tech Reviews\",\n  \"platforms\": [\"YouTube\",\"TikTok\"],\n  \"credentials\": {\"api_key\":\"abc123\"},\n  \"language\": \"en\",\n  \"status\": \"active\"\n}"}}}, {"name": "Get Account", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts/1"}}, {"name": "Update Account", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts/1", "body": {"mode": "raw", "raw": "{\n  \"name\": \"MyUpdatedChannel\"\n}"}}}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts/1"}}]}, {"name": "Video Tasks", "item": [{"name": "List Video Tasks", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks"}}, {"name": "Save Draft Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_draft", "body": {"mode": "raw", "raw": "{\n  \"video_type\": \"faceless\",\n  \"context\": \"Create an educational video about renewable energy sources like solar and wind power. Explain their benefits and environmental impact.\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_voice\": 1,\n  \"video_style\": \"educational\",\n  \"bgm\": 1,\n  \"image_provider\": \"flux.1-schnell\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"landscape\",\n  \"duration\": 60,\n  \"auto_approval_each_stage\": true\n}"}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.task_id) {", "    pm.environment.set('draft_task_id', json.task_id);", "}"]}}]}, {"name": "Update Draft Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_draft", "body": {"mode": "raw", "raw": "{\n  \"id\": {{draft_task_id}},\n  \"video_type\": \"avatar\",\n  \"context\": \"Updated: Create a comprehensive guide about artificial intelligence\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"elevenlabs\",\n  \"tts_voice\": \"1\",\n  \"video_style\": \"cinematic\",\n  \"bgm\": \"calm_piano\",\n  \"image_provider\": \"flux.1-schnell\",\n  \"orientation\": \"portrait\",\n  \"duration\": 90\n}"}}}, {"name": "Save and Proceed Task (Start Video Creation)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_and_proceed", "body": {"mode": "raw", "raw": "{\n  \"video_type\": \"faceless\",\n  \"context\": \"Create an engaging video about the future of electric vehicles and sustainable transportation. Cover battery technology, charging infrastructure, and environmental benefits.\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_voice\": 3,\n  \"video_style\": \"documentary\",\n  \"bgm\": 3,\n  \"image_provider\": \"flux.1-schnell\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"landscape\",\n  \"duration\": 120,\n  \"auto_approval_each_stage\": true\n}"}, "description": "Create task and start video creation with automatic stage progression"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.task_id) {", "    pm.environment.set('production_task_id', json.task_id);", "}", "if (json.video_id) {", "    pm.environment.set('active_video_id', json.video_id);", "}"]}}]}, {"name": "Save and Proceed Task (Manual Approval)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_and_proceed", "body": {"mode": "raw", "raw": "{\n  \"video_type\": \"faceless\",\n  \"context\": \"Create a professional video about AI in healthcare.\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"elevenlabs\",\n  \"tts_voice\": \"1\",\n  \"video_style\": \"professional\",\n  \"bgm\": \"1\",\n  \"image_provider\": \"flux.1-schnell\",\n  \"orientation\": \"landscape\",\n  \"duration\": 90,\n  \"auto_approval_each_stage\": false\n}"}, "description": "Create task requiring manual approval at each stage"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.task_id) {", "    pm.environment.set('manual_task_id', json.task_id);", "}", "if (json.video_id) {", "    pm.environment.set('manual_video_id', json.video_id);", "}"]}}]}, {"name": "Get Video Task", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/1"}}, {"name": "Delete Video Task", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/1"}}]}, {"name": "Videos", "item": [{"name": "List Videos", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos"}}, {"name": "Get Video Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}"}}, {"name": "Get Video Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/status"}}, {"name": "Update Video", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}", "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Video Title\",\n  \"description\": \"Updated video description with more details\"\n}"}}}]}, {"name": "Media Management", "description": "Endpoints for managing video media assets, tracks, clips, and generations", "item": [{"name": "List Tracks", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tracks", "description": "Get all tracks for user's videos"}}, {"name": "Create Track", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tracks", "body": {"mode": "raw", "raw": "{\n  \"video\": {{active_video_id}},\n  \"type\": \"video\",\n  \"layer\": 0\n}"}, "description": "Create a new track for a video"}}, {"name": "Get Track Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tracks/1", "description": "Get details of a specific track"}}, {"name": "List Media Generations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/media-generations", "description": "Get all media generations for user's videos"}}, {"name": "Create Media Generation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/media-generations", "body": {"mode": "raw", "raw": "{\n  \"video\": {{active_video_id}},\n  \"prompt\": \"A beautiful landscape\",\n  \"media_type\": \"image\",\n  \"media_provider\": \"flux.1-schnell\"\n}"}, "description": "Create a new media generation request"}}, {"name": "Get Media Generation Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/media-generations/1", "description": "Get details of a specific media generation"}}, {"name": "List Media Assets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/media-assets", "description": "Get all media assets for user's videos"}}, {"name": "Create Media Asset", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/media-assets", "body": {"mode": "raw", "raw": "{\n  \"video\": {{active_video_id}},\n  \"generation\": 1,\n  \"source_path\": \"https://example.com/image.jpg\",\n  \"type\": \"image\",\n  \"duration\": 5\n}"}, "description": "Create a new media asset"}}, {"name": "Get Media Asset Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/media-assets/1", "description": "Get details of a specific media asset"}}, {"name": "List Clips", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/clips", "description": "Get all clips for user's videos"}}, {"name": "Create Clip", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/clips", "body": {"mode": "raw", "raw": "{\n  \"track\": 1,\n  \"media\": 1,\n  \"sequence\": 1,\n  \"in_point\": 0.0,\n  \"out_point\": 5.0,\n  \"start_time\": 0.0,\n  \"opacity\": 1.0\n}"}, "description": "Create a new clip"}}, {"name": "<PERSON> Clip <PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/clips/1", "description": "Get details of a specific clip"}}]}, {"name": "Payment System", "description": "Complete payment system with packages, subscriptions, usage analytics and payment processing", "item": [{"name": "Packages & Subscriptions", "item": [{"name": "List Packages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/packages", "description": "List all available packages with pricing and features"}}, {"name": "Get Package Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/packages/1", "description": "Get detailed information about a specific package"}}, {"name": "Get Packages with Payment Methods", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/packages/with_payment_methods", "description": "Get packages with available payment methods for each gateway"}}, {"name": "List User Packages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/user-packages", "description": "List user's current subscriptions and packages"}}, {"name": "Get User Package Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/user-packages/1", "description": "Get details of a specific user package"}}]}, {"name": "Usage Analytics", "item": [{"name": "Get Usage Analytics Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/usage-analytics", "description": "Get comprehensive usage analytics including trends, predictions, and limits"}}, {"name": "Get Current Usage Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/usage-analytics/current_usage", "description": "Get current usage summary only"}}, {"name": "Check Usage Limits", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/usage-analytics/check_limits", "body": {"mode": "raw", "raw": "{\n  \"limits\": {\n    \"video_seconds\": 120\n  }\n}"}, "description": "Check multiple usage limits at once"}}]}, {"name": "Upgrade Recommendations", "item": [{"name": "Get Upgrade Recommendations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/upgrade-recommendations", "description": "Get upgrade recommendations based on current usage patterns"}}, {"name": "Get Scenario-based Recommendations", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/upgrade-recommendations", "body": {"mode": "raw", "raw": "{\n  \"exceeded_usage_type\": \"video_seconds\",\n  \"required_amount\": 1000\n}"}, "description": "Get recommendations for specific usage scenario"}}]}, {"name": "Payment History", "item": [{"name": "Get Payment History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/payment-history", "description": "Get user's payment history"}}, {"name": "Get Specific Payment", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/payment-history/1", "description": "Get details of a specific payment"}}]}, {"name": "Payment Gateway Information", "item": [{"name": "Get Gateway Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/gateway-info", "description": "Get information about available payment gateways, their status, and supported features"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "console.log('Available Gateways:', JSON.stringify(json.gateways, null, 2));", "console.log('Default Gateway:', json.default_gateway);", "console.log('Test Mode:', json.test_mode);"]}}]}, {"name": "Get Payment Methods", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/payment-methods?gateway=mock", "description": "Get supported payment methods for a specific gateway"}}]}, {"name": "Payment Sessions", "item": [{"name": "Create Payment Session (Mock Gateway)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/payment-sessions/create_session", "body": {"mode": "raw", "raw": "{\n  \"package_id\": 1,\n  \"gateway_name\": \"mock\",\n  \"payment_methods\": [\"card\", \"upi\"],\n  \"callback_url\": \"http://localhost:3000/payment/callback\",\n  \"metadata\": {\n    \"source\": \"postman_test\",\n    \"user_agent\": \"PostmanRuntime\"\n  }\n}"}, "description": "Create a payment session using mock gateway for testing"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.session_id) {", "    pm.environment.set('payment_session_id', json.session_id);", "    console.log('Payment Session ID:', json.session_id);", "}", "if (json.gateway_session_id) {", "    pm.environment.set('gateway_session_id', json.gateway_session_id);", "    console.log('Gateway Session ID:', json.gateway_session_id);", "}", "console.log('Payment URL:', json.payment_url);"]}}]}, {"name": "Get Payment Session", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/payment-sessions/{{payment_session_id}}", "description": "Get details of a specific payment session"}}, {"name": "List Payment Sessions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/payment-sessions", "description": "List all payment sessions for the authenticated user"}}, {"name": "Get Payment Session Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/payment-sessions/{{payment_session_id}}/status", "description": "Get current status of a payment session"}}, {"name": "Verify Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/payment-sessions/{{payment_session_id}}/verify", "body": {"mode": "raw", "raw": "{\n  \"gateway_data\": {\n    \"razorpay_payment_id\": \"pay_example123\",\n    \"razorpay_order_id\": \"order_example123\",\n    \"razorpay_signature\": \"signature_example123\"\n  }\n}"}, "description": "Verify payment completion with gateway-specific data"}}]}, {"name": "Payment Analytics", "item": [{"name": "Get Payment Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/analytics", "description": "Get payment analytics and success rates across gateways"}}]}, {"name": "Subscription Management", "item": [{"name": "Get Current Subscription", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/subscription", "description": "Get current subscription details"}}, {"name": "Cancel Subscription", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/subscription", "description": "Cancel current subscription"}}]}, {"name": "Webhooks", "item": [{"name": "Mock Gateway Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/payments/webhook/mock", "body": {"mode": "raw", "raw": "{\n  \"event\": \"payment.captured\",\n  \"session_id\": \"{{payment_session_id}}\",\n  \"gateway_session_id\": \"{{gateway_session_id}}\",\n  \"amount\": 2999,\n  \"currency\": \"INR\",\n  \"status\": \"success\",\n  \"payment_method\": \"upi\",\n  \"created_at\": \"2024-01-01T12:00:00Z\"\n}"}, "description": "Simulate webhook from mock gateway"}}, {"name": "Razorpay Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Razorpay-Signature", "value": "mock_signature_for_testing"}], "url": "{{base_url}}/payments/webhook/razorpay", "body": {"mode": "raw", "raw": "{\n  \"event\": \"payment.captured\",\n  \"payload\": {\n    \"payment\": {\n      \"entity\": {\n        \"id\": \"pay_example123\",\n        \"amount\": 299900,\n        \"currency\": \"INR\",\n        \"status\": \"captured\",\n        \"order_id\": \"{{gateway_session_id}}\",\n        \"method\": \"upi\",\n        \"created_at\": 1704110400\n      }\n    }\n  }\n}"}, "description": "Simulate webhook from Razorpay"}}]}]}, {"name": "Video Creation Flow", "item": [{"name": "Video Retry System", "item": [{"name": "Retry Script Generation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/script_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Script quality needs improvement - retry with enhanced prompts\"\n}"}, "description": "Retry script generation stage"}}, {"name": "Retry Voice Generation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/voice_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Voice quality or pronunciation needs improvement\"\n}"}, "description": "Retry voice generation stage"}}, {"name": "Retry Caption Generation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/caption_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Caption timing or content needs refinement\"\n}"}, "description": "Retry caption generation stage"}}, {"name": "Retry Image Prompt Generation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/image_prompt_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Image prompts need better alignment with script content\"\n}"}, "description": "Retry image prompt generation stage"}}, {"name": "Retry Image Generation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/image_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Generated images need better quality or accuracy\"\n}"}, "description": "Retry image generation stage"}}, {"name": "Retry Track Creation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/track_creation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Track configuration needs adjustment\"\n}"}, "description": "Retry track creation stage"}}, {"name": "<PERSON><PERSON> Clip <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/clip_creation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Clip timing and transitions need adjustment\"\n}"}, "description": "Retry clip creation stage"}}, {"name": "Retry Video Composition", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/video_composition", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Final video composition needs quality improvements\"\n}"}, "description": "Retry video composition stage"}}]}, {"name": "Manual Flow Control", "item": [{"name": "Proceed to Next Stage (Approve)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/proceed_to_next_stage", "body": {"mode": "raw", "raw": "{\n  \"approve\": true,\n  \"feedback\": \"Stage looks good, approved for progression\",\n  \"reason\": \"Manual approval after review\"\n}"}}}, {"name": "Reject Current Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/proceed_to_next_stage", "body": {"mode": "raw", "raw": "{\n  \"approve\": false,\n  \"feedback\": \"Quality needs improvement. Script requires more engaging content and better flow.\",\n  \"reason\": \"Content quality review - needs revision\"\n}"}}}]}, {"name": "Callback Endpoints", "item": [{"name": "Script Generation Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/{{active_video_id}}/script_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_script_001\",\n  \"script\": \"Welcome to our comprehensive guide on renewable energy...\",\n  \"title\": \"Renewable Energy: Powering the Future\",\n  \"description\": \"An in-depth look at renewable energy sources.\"\n}"}}}, {"name": "Voice Generation Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/{{active_video_id}}/voice_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_voice_001\",\n  \"speech_url\": \"https://storage.example.com/audio/voice_{{active_video_id}}.mp3\"\n}"}}}, {"name": "Caption Generation Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/{{active_video_id}}/caption_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_caption_001\",\n  \"caption\": \"Welcome to our guide [0:00-0:02]. Let's explore renewable energy [0:03-0:06].\",\n  \"parsed_caption\": [\n    {\n      \"text\": \"Welcome to our guide\",\n      \"start_time\": 0.0,\n      \"end_time\": 2.0\n    },\n    {\n      \"text\": \"Let's explore renewable energy\",\n      \"start_time\": 3.0,\n      \"end_time\": 6.0\n    }\n  ]\n}"}, "description": "Simulate caption generation callback from provider"}}, {"name": "Image Prompt Generation Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/{{active_video_id}}/image_prompt_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_prompts_001\",\n  \"output\": {\n    \"media_generations\": [\n      {\n        \"id\": 1,\n        \"prompt\": \"A modern solar panel installation on a rooftop with blue sky background\"\n      },\n      {\n        \"id\": 2,\n        \"prompt\": \"Wind turbines in a green field generating clean energy\"\n      }\n    ]\n  }\n}"}, "description": "Simulate image prompt generation callback from provider"}}, {"name": "Image Generation Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/{{active_video_id}}/image_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_images_001\",\n  \"generated_images\": [\n    {\n      \"image_url\": \"https://storage.example.com/images/solar_panel_1.jpg\",\n      \"sequence_order\": 1,\n      \"duration\": 3,\n      \"metadata\": {\n        \"prompt\": \"Solar panel installation\",\n        \"provider\": \"flux.1-schnell\"\n      }\n    },\n    {\n      \"image_url\": \"https://storage.example.com/images/wind_turbine_1.jpg\",\n      \"sequence_order\": 2,\n      \"duration\": 3,\n      \"metadata\": {\n        \"prompt\": \"Wind turbines in field\",\n        \"provider\": \"flux.1-schnell\"\n      }\n    }\n  ]\n}"}, "description": "Simulate image generation callback from provider"}}, {"name": "Track Creation Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/{{active_video_id}}/track_creation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_tracks_001\",\n  \"tracks\": [\n    {\n      \"type\": \"video\",\n      \"layer\": 0\n    },\n    {\n      \"type\": \"audio\",\n      \"layer\": 0\n    }\n  ]\n}"}, "description": "Simulate track creation callback (internal stage)"}}, {"name": "C<PERSON> <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/{{active_video_id}}/clip_creation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_clips_001\",\n  \"clips\": [\n    {\n      \"track_type\": \"video\",\n      \"layer\": 0,\n      \"media_asset_id\": 1,\n      \"start_time\": 0.0,\n      \"in_point\": 0.0,\n      \"out_point\": 3.0,\n      \"opacity\": 1.0\n    },\n    {\n      \"track_type\": \"audio\",\n      \"layer\": 0,\n      \"start_time\": 0.0,\n      \"in_point\": 0.0,\n      \"out_point\": 60.0,\n      \"volume\": 1.0\n    }\n  ]\n}"}, "description": "Simulate clip creation callback (internal stage)"}}, {"name": "Video Composition Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/{{active_video_id}}/video_composition", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_compose_001\",\n  \"production_url\": \"https://cdn.example.com/videos/final_{{active_video_id}}.mp4\",\n  \"duration\": 120,\n  \"thumbnail_url\": \"https://cdn.example.com/thumbnails/video_{{active_video_id}}.jpg\"\n}"}}}]}]}, {"name": "Configurations", "item": [{"name": "Get Configuration Options", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/configurations", "description": "Get all available options for video creation including TTS voices, BGM options, video styles, orientations, durations, and image providers"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "console.log('=== Available TTS Voices ===');", "if (json.tts_voices) {", "    json.tts_voices.forEach(function(voice) {", "        console.log('ID: ' + voice.id + ', Provider: ' + voice.provider + ', Name: ' + voice.name + ', Display: ' + voice.display_name);", "    });", "}", "console.log('\\n=== Available BGM Options ===');", "if (json.bgms) {", "    json.bgms.forEach(function(bgm) {", "        console.log('ID: ' + bgm.id + ', Name: ' + bgm.name + ', Display: ' + bgm.display_name + ', Genre: ' + bgm.genre);", "    });", "}"]}}]}]}]}