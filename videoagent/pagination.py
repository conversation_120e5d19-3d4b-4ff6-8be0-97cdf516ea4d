from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response

class CustomPagination(PageNumberPagination):
    # default page_size
    page_size = 10
    # allow client to set “limit”
    page_size_query_param = 'limit'
    # default page_query_param is already 'page'
    page_query_param = 'page'

    def get_paginated_response(self, data):
        page_size = self.get_page_size(self.request) or self.page.paginator.per_page
        offset = (self.page.number - 1) * page_size
        return Response({
            'total_count': self.page.paginator.count,
            'total_pages': self.page.paginator.num_pages,
            'current_page': self.page.number,
            'offset': offset,
            'results': data
        })