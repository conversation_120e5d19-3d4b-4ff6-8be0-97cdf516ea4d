"""
Django settings for videoagent project.

Generated by 'django-admin startproject' using Django 5.2.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os
from decouple import config, Csv
from datetime import timedelta

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config('SECRET_KEY', default='django-insecure-^=r##ix04!18#ssc98_u6iizqio-*0$k(%)&7-e16sylev-cx-')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = config('DEBUG', default=False, cast=bool)

ALLOWED_HOSTS = config('ALLOWED_HOSTS', default='*', cast=Csv())


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',
    
    # Third-party apps
    'rest_framework',
    'rest_framework.authtoken',
    'corsheaders',
    'django_extensions',
    'dj_rest_auth',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',
    
    # Custom apps
    'authentication',
    'videos',
    'accounts',
    'payments',
    'events',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'allauth.account.middleware.AccountMiddleware',  # Required by django-allauth
]

ROOT_URLCONF = 'videoagent.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'videoagent.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': config('DB_NAME', default='videoagent'),
        'USER': config('DB_USER', default='postgres'),
        'PASSWORD': config('DB_PASSWORD', default='postgres'),
        'HOST': config('DB_HOST', default='localhost'),
        'PORT': config('DB_PORT', default='5432'),
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Custom User model
AUTH_USER_MODEL = 'authentication.User'

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.TokenAuthentication',
        # 'rest_framework.authentication.SessionAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_PAGINATION_CLASS': 'videoagent.pagination.CustomPagination',
    'PAGE_SIZE': 10,
}

# dj-rest-auth settings
REST_AUTH = {
    'USE_JWT': True,
    'JWT_AUTH_COOKIE': 'videoagent-auth',
    'JWT_AUTH_REFRESH_COOKIE': 'videoagent-refresh-token',
    'JWT_AUTH_SECURE': False,
    'JWT_AUTH_HTTPONLY': True,
    'JWT_AUTH_SAMESITE': 'Lax',
    'JWT_AUTH_RETURN_EXPIRATION': True,
    'TOKEN_MODEL': None,
}

# Authentication settings
AUTHENTICATION_BACKENDS = (
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
)

SITE_ID = 1

ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
ACCOUNT_AUTHENTICATION_METHOD = 'email'
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_UNIQUE_EMAIL = True

# Email configuration
EMAIL_BACKEND = config('EMAIL_BACKEND', default='django.core.mail.backends.console.EmailBackend')
EMAIL_HOST = config('EMAIL_HOST', default='')
EMAIL_PORT = config('EMAIL_PORT', default=587, cast=int)
EMAIL_USE_TLS = config('EMAIL_USE_TLS', default=True, cast=bool)
EMAIL_HOST_USER = config('EMAIL_HOST_USER', default='')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD', default='')
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL', default='<EMAIL>')

# Enhanced Multi-Payment Gateway System Configuration
DEFAULT_PAYMENT_GATEWAY = config('DEFAULT_PAYMENT_GATEWAY', default='razorpay')
BASE_URL = config('BASE_URL', default='https://videoai.syncu.in')
# Razorpay Configuration
RAZORPAY_API_KEY = config('RAZORPAY_API_KEY', default='')
RAZORPAY_API_SECRET = config('RAZORPAY_API_SECRET', default='')
RAZORPAY_WEBHOOK_SECRET = config('RAZORPAY_WEBHOOK_SECRET', default='')
RAZORPAY_TEST_MODE = config('RAZORPAY_TEST_MODE', default=True, cast=bool)

# CORS settings
CORS_ALLOW_ALL_ORIGINS = config('CORS_ALLOW_ALL_ORIGINS', default=True, cast=bool)
CORS_ALLOWED_ORIGINS = config('CORS_ALLOWED_ORIGINS', default='http://localhost:3000,http://localhost:8000', cast=Csv())

# N8N webhook settings
N8N_WEBHOOK_BASE_URL = config('N8N_WEBHOOK_BASE_URL', default='https://n8n.syncu.in/webhook/')
N8N_CREATE_VIDEO_API = config('N8N_CREATE_VIDEO_API', default='a8a2c6b9-36dd-48f7-b4ef-7e83907afa74/video-agent/')

STORAGE_BASE_URL = config('STORAGE_BASE_URL', default='https://miniio-api.syncu.in/nca/')

# NCA Toolkit API settings
NCA_API_BASE_URL = config('NCA_API_BASE_URL', default='https://nca-toolkit.syncu.in')
NCA_API_KEY = config('NCA_API_KEY', default='')
NCA_REQUEST_TIMEOUT = config('NCA_REQUEST_TIMEOUT', default=60, cast=int)

# Kafka settings
KAFKA_BOOTSTRAP_SERVERS = config('KAFKA_BOOTSTRAP_SERVERS', default='localhost:9092')
KAFKA_CLIENT_ID = config('KAFKA_CLIENT_ID', default='video-creation-service')

# Celery settings (for background tasks)
CELERY_BROKER_URL = config('CELERY_BROKER_URL', default='redis://localhost:6379/0')
CELERY_RESULT_BACKEND = config('CELERY_RESULT_BACKEND', default='redis://localhost:6379/0')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# Google OAuth callback URL
GOOGLE_CALLBACK_URL = config('GOOGLE_CALLBACK_URL', default='http://localhost:8000/api/auth/google/callback/')
CALLBACK_BASE_URL = config('CALLBACK_BASE_URL', default='https://videoai.syncu.in/api/videos/callback/recieve-response/')

# RunPod API settings
RUNPOD_API_KEY = config('RUNPOD_API_KEY', default='')
RUNPOD_BASE_URL = config('RUNPOD_BASE_URL', default='https://api.runpod.ai/v2')
RUNPOD_ENDPOINT_ID = config('RUNPOD_ENDPOINT_ID', default='')
RUNPOD_REQUEST_TIMEOUT = config('RUNPOD_REQUEST_TIMEOUT', default=300, cast=int)
RUNPOD_MAX_RETRIES = config('RUNPOD_MAX_RETRIES', default=3, cast=int)
RUNPOD_POLL_INTERVAL = config('RUNPOD_POLL_INTERVAL', default=5, cast=int)

# Together AI API settings
TOGETHER_AI_API_ENDPOINT = config('TOGETHER_AI_API_ENDPOINT', default='https://api.together.ai/v1/images/generations')
TOGETHER_AI_API_KEY = config('TOGETHER_AI_API_KEY', default='')
TOGETHER_AI_REQUEST_TIMEOUT = config('TOGETHER_AI_REQUEST_TIMEOUT', default=120, cast=int)
TOGETHER_AI_DEFAULT_MODEL = config('TOGETHER_AI_DEFAULT_MODEL', default='black-forest-labs/FLUX.1-schnell')
TOGETHER_AI_MAX_RETRIES = config('TOGETHER_AI_MAX_RETRIES', default=3, cast=int)
# Custom Exception Handler for Usage Tracking
REST_FRAMEWORK['EXCEPTION_HANDLER'] = 'payments.exceptions.custom_exception_handler'

# Logging configuration
LOG_LEVEL = config('LOG_LEVEL', default='WARNING')

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'logs/debug.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': LOG_LEVEL,
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': LOG_LEVEL,
            'propagate': False,
        },
        'payments': {
            'handlers': ['console'],
            'level': LOG_LEVEL,
            'propagate': False,
        },
        'payments.services': {
            'handlers': ['console'],
            'level': LOG_LEVEL,
            'propagate': False,
        },
        'payments.gateways': {
            'handlers': ['console'],
            'level': LOG_LEVEL,
            'propagate': False,
        },
        'payments.gateways.razorpay_gateway': {
            'handlers': ['console'],
            'level': LOG_LEVEL,
            'propagate': False,
        },
    },
}
