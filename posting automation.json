{"nodes": [{"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "648715328327070", "edge": "photos", "sendBinaryData": true, "binaryPropertyName": "data", "options": {"queryParameters": {"parameter": [{"name": "message", "value": "={{ $('Baserow').item.json.description }}"}]}}}, "id": "3c455580-79b6-4769-9df2-3c82d8bc4a69", "name": "Facebook Post", "type": "n8n-nodes-base.facebookGraphApi", "position": [1480, 340], "typeVersion": 1, "alwaysOutputData": true, "credentials": {"facebookGraphApi": {"id": "NHcXtjqhFrCLfRlF", "name": "Facebook Graph Bhakti Payanam"}}, "onError": "continueRegularOutput"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "86d44336-bab7-422f-9266-fcb513252d19", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.value }}", "rightValue": "instagram"}], "combinator": "and"}, "renameOutput": true, "outputKey": " 2️⃣Instagram"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "29f37628-6381-46af-babb-74bf00b4a849", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.value }}", "rightValue": "facebook"}], "combinator": "and"}, "renameOutput": true, "outputKey": " 3️⃣Facebook"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4d690442-197c-4ff9-b176-b55dfabaecc9", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.value }}", "rightValue": "youtube"}], "combinator": "and"}, "renameOutput": true, "outputKey": "6️⃣YouTube"}]}, "options": {}}, "id": "cb38e524-4124-4a84-bce7-7c86306b4774", "name": "Social Media Publishing Router", "type": "n8n-nodes-base.switch", "position": [460, 340], "typeVersion": 3.2}, {"parameters": {"assignments": {"assignments": [{"id": "da8fe7e3-e74d-46b6-91eb-1bf4432b73b0", "name": "output", "type": "string", "value": "={{ $('Social Media Publishing Router').item.json.data.route }}  \n{{ $('Social Media Publishing Router').item.json.data.social_content.root_schema.name }}\n{{ $('Social Media Publishing Router').item.json.data.social_content.schema.caption }}\n![{{ $('Social Media Publishing Router').item.json.data.social_content.root_schema.name }}]({{ $('Social Media Publishing Router').item.json.data.social_image.thumb.url }})"}, {"id": "7f8f0c50-c996-4654-9286-faec6a51fdef", "name": "Status", "value": "Instagram Post Posted Successfully", "type": "string"}]}, "options": {}}, "id": "96dc0073-8d96-40de-ab00-fe8ec437be4e", "name": "Instagram Response", "type": "n8n-nodes-base.set", "position": [2520, 0], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "e349e314-2967-456f-856a-85727bdf94f3", "name": "output", "type": "string", "value": "={{ $('Social Media Publishing Router').item.json.data.route }}\n\n{{ $('Social Media Publishing Router').item.json.data.social_content.root_schema.name }}\n\n{{ $('Social Media Publishing Router').item.json.data.social_content.schema.post }}\n\n![{{ $('Social Media Publishing Router').item.json.data.social_content.root_schema.name }}]({{ $('Social Media Publishing Router').item.json.data.social_image.thumb.url }})"}]}, "options": {}}, "id": "0db7925d-396b-40d0-8816-aa2dccfb5002", "name": "Facebook Response", "type": "n8n-nodes-base.set", "position": [1680, 340], "typeVersion": 3.4}, {"parameters": {"operation": "update", "databaseId": 168, "tableId": 683, "rowId": "={{ $('Baserow').item.json.id }}", "fieldsUi": {"fieldValues": [{"fieldId": 6627, "fieldValue": "posted"}]}}, "type": "n8n-nodes-base.baserow", "typeVersion": 1, "position": [2360, 0], "id": "dad1340f-23e5-49d0-b0da-bc4da926211f", "name": "Baserow1", "credentials": {"baserowApi": {"id": "NNeGK9XF9Et3nqI6", "name": "Baserow staging"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "856c35e3-66b2-4c33-8af6-d4c25a468387", "leftValue": "={{ $json.statusCode }}", "rightValue": 200, "operator": {"type": "number", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2040, 20], "id": "11458e08-6ab9-48a1-abb4-ab7efc318a42", "name": "If"}, {"parameters": {"url": "={{ $('Baserow').item.json['Production Video URL'] }}", "options": {"response": {"response": {"fullResponse": true, "responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2180, 560], "id": "d1cc639d-2c2f-4ca5-8567-fc182afaf03d", "name": "HTTP Request"}, {"parameters": {"jsCode": "for (const item of $input.all()) {\n  item.binary.data.mimeType = 'video/webm'\n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2340, 560], "id": "d2af5f67-e596-4b0c-87c9-a4ba9cee3066", "name": "Code"}, {"parameters": {"method": "PUT", "url": "= {{ $('@BhaktiPayanam').item.json.headers.location }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "video/webm"}]}, "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "id": "d11cd33e-a625-4350-9c24-1c6f6652bea3", "name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2520, 560], "credentials": {"youTubeOAuth2Api": {"id": "yfSbwE3xvX33ApR1", "name": "YouTube account @Chinna Padam"}}}, {"parameters": {"operation": "update", "databaseId": 169, "tableId": 693, "rowId": "={{ $('Baserow').item.json['Record ID'] }}", "fieldsUi": {"fieldValues": [{"fieldId": 6834, "fieldValue": "Posted"}]}}, "type": "n8n-nodes-base.baserow", "typeVersion": 1, "position": [2680, 560], "id": "2b6128c2-2847-4614-a719-01bcbd396bce", "name": "youtube status", "credentials": {"baserowApi": {"id": "C9KbHIKvBTxXetde", "name": "Baserow production"}}}, {"parameters": {"path": "post/:RowID", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-180, 340], "id": "e984b1b1-c6cf-418e-9757-6f360b754ad9", "name": "Webhook", "webhookId": "9503b9f0-0a5d-4f76-bcbb-78342bf88ac7"}, {"parameters": {"operation": "get", "databaseId": 169, "tableId": 693, "rowId": "={{ $json.params.RowID }}"}, "type": "n8n-nodes-base.baserow", "typeVersion": 1, "position": [20, 340], "id": "98c33f17-cfdc-4658-831c-90897a3d5db9", "name": "Baserow", "credentials": {"baserowApi": {"id": "C9KbHIKvBTxXetde", "name": "Baserow production"}}}, {"parameters": {"fieldToSplitOut": "Platforms", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [260, 340], "id": "aad9ebc2-49f7-4773-b87a-51db97ffc005", "name": "Split Out2"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/upload/youtube/v3/videos?part=snippet,status&uploadType=resumable", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Upload-Content-Type", "value": "video/webm"}]}, "sendBody": true, "contentType": "raw", "rawContentType": "RAW/JSON", "body": "={\n  \"snippet\": {\n    \"title\": \"{{ $('Baserow').item.json.title }} | #BhaktiPayanam\",\n    \"description\": \"{{ $('Baserow').item.json.description }}\",\n    \"tags\": \"{{ $('Baserow').item.json.description.match(/#[^ ]*/g)}}\",\n    \"categoryId\": \"22\",\n    \"defaultLanguage\": \"ta\",\n    \"defaultAudioLanguage\": \"ta\"\n  },\n  \"status\": {\n    \"privacyStatus\": \"private\",\n    \"publishAt\":\"{{ $('Baserow').item.json['Publish at'] ?? $now }}\",\n    \"license\": \"youtube\",\n    \"embeddable\": \"true\",\n    \"publicStatsViewable\": \"true\",\n    \"madeForKids\": \"false\"\n  }\n}\n", "options": {"response": {"response": {"fullResponse": true}}}}, "id": "83495da0-af5b-4d0a-b169-d1088919e265", "name": "@BhaktiPayanam", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1980, 560], "credentials": {"youTubeOAuth2Api": {"id": "BHx5PgYsaiZA4eer", "name": "YouTube account @BhaktiPayanam"}}}, {"parameters": {"url": "={{ $('Baserow').item.json['Production Video URL'] }}", "options": {"response": {"response": {"fullResponse": true, "responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2180, 780], "id": "816673ea-29ce-47af-893b-229697ff68ff", "name": "HTTP Request2"}, {"parameters": {"jsCode": "for (const item of $input.all()) {\n  item.binary.data.mimeType = 'video/webm'\n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2360, 780], "id": "68a3f9be-3e72-46a0-8098-8181d42c5e8b", "name": "Code1"}, {"parameters": {"method": "PUT", "url": "= {{ $('@GodTrailIndia').item.json.headers.location }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "video/webm"}]}, "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "id": "fd68b0f6-4fd5-4066-a26a-233d01f74d06", "name": "HTTP Request3", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2520, 780], "credentials": {"youTubeOAuth2Api": {"id": "yfSbwE3xvX33ApR1", "name": "YouTube account @Chinna Padam"}}}, {"parameters": {"operation": "update", "databaseId": 169, "tableId": 693, "rowId": "={{ $('Baserow').item.json['Record ID'] }}", "fieldsUi": {"fieldValues": [{"fieldId": 6834, "fieldValue": "Posted"}]}}, "type": "n8n-nodes-base.baserow", "typeVersion": 1, "position": [2700, 780], "id": "861c2bc2-6b77-40f9-b448-9de788b4c881", "name": "youtube status1", "credentials": {"baserowApi": {"id": "C9KbHIKvBTxXetde", "name": "Baserow production"}}}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/upload/youtube/v3/videos?part=snippet,status&uploadType=resumable", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Upload-Content-Type", "value": "video/webm"}]}, "sendBody": true, "contentType": "raw", "rawContentType": "RAW/JSON", "body": "={\n  \"snippet\": {\n    \"title\": \"{{ $('Baserow').item.json.title }} | #GodTrailIndia\",\n    \"description\": \"{{ $('Baserow').item.json.description }}\",\n    \"tags\": \"{{ $('Baserow').item.json.description.match(/#[^ ]*/g)}}\",\n    \"categoryId\": \"22\",\n    \"defaultLanguage\": \"en\",\n    \"defaultAudioLanguage\": \"en\"\n  },\n  \"status\": {\n    \"privacyStatus\": \"private\",\n    \"publishAt\":\"{{ $('Baserow').item.json['Publish at'] ?? $now }}\",\n    \"license\": \"youtube\",\n    \"embeddable\": \"true\",\n    \"publicStatsViewable\": \"true\",\n    \"madeForKids\": \"false\"\n  }\n}\n", "options": {"response": {"response": {"fullResponse": true}}}}, "id": "4d9d3ab3-c315-4472-9c81-461d6bdcbb7c", "name": "@GodTrailIndia", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1980, 780], "credentials": {"youTubeOAuth2Api": {"id": "9dBsZ7ngBSp6ZFLm", "name": "YouTube account @GodTrailIndia"}}}, {"parameters": {"url": "={{ $('Baserow').item.json['Production Video URL'] }}", "options": {"response": {"response": {"fullResponse": true, "responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2180, 980], "id": "b993e1ec-57ec-4e47-b73f-410543c65556", "name": "HTTP Request4"}, {"parameters": {"jsCode": "for (const item of $input.all()) {\n  item.binary.data.mimeType = 'video/webm'\n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2340, 980], "id": "2361553d-0a53-4ea1-9546-676c5bb1a987", "name": "Code2"}, {"parameters": {"method": "PUT", "url": "= {{ $('@BhagwantKatha').item.json.headers.location }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "video/webm"}]}, "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "id": "5c105b1b-13e6-4013-9b4c-55d51d77f100", "name": "HTTP Request5", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2520, 980], "credentials": {"youTubeOAuth2Api": {"id": "yfSbwE3xvX33ApR1", "name": "YouTube account @Chinna Padam"}}}, {"parameters": {"operation": "update", "databaseId": 169, "tableId": 693, "rowId": "={{ $('Baserow').item.json['Record ID'] }}", "fieldsUi": {"fieldValues": [{"fieldId": 6834, "fieldValue": "Posted"}]}}, "type": "n8n-nodes-base.baserow", "typeVersion": 1, "position": [2700, 980], "id": "afe534cc-5300-4e76-be9d-bfec5b282122", "name": "youtube status2", "credentials": {"baserowApi": {"id": "C9KbHIKvBTxXetde", "name": "Baserow production"}}}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/upload/youtube/v3/videos?part=snippet,status&uploadType=resumable", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Upload-Content-Type", "value": "video/webm"}]}, "sendBody": true, "contentType": "raw", "rawContentType": "RAW/JSON", "body": "={\n  \"snippet\": {\n    \"title\": \"{{ $('Baserow').item.json.title }} | #BhagwantKatha\",\n    \"description\": \"{{ $('Baserow').item.json.description }}\",\n    \"tags\": \"{{ $('Baserow').item.json.description.match(/#[^ ]*/g)}}\",\n    \"categoryId\": \"22\",\n    \"defaultLanguage\": \"hi\",\n    \"defaultAudioLanguage\": \"hi\"\n  },\n  \"status\": {\n    \"privacyStatus\": \"private\",\n    \"publishAt\":\"{{ $('Baserow').item.json['Publish at'] ?? $now }}\",\n    \"license\": \"youtube\",\n    \"embeddable\": \"true\",\n    \"publicStatsViewable\": \"true\",\n    \"madeForKids\": \"false\"\n  }\n}\n", "options": {"response": {"response": {"fullResponse": true}}}}, "id": "8b1ecdc0-6503-4b32-88af-f9aa8d856111", "name": "@BhagwantKatha", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1980, 980], "credentials": {"youTubeOAuth2Api": {"id": "4wJHartAbbxPleYN", "name": "YouTube account @BhagwantKatha"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "29f37628-6381-46af-babb-74bf00b4a849", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Baserow').item.json.Account[0].value }}", "rightValue": "Bakthi Payanam"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Bakthi Payanam"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4d690442-197c-4ff9-b176-b55dfabaecc9", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Baserow').item.json.Account[0].value }}", "rightValue": "God Trail India"}], "combinator": "and"}, "renameOutput": true, "outputKey": "God Trail India"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "86d44336-bab7-422f-9266-fcb513252d19", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('Baserow').item.json.Account[0].value }}", "rightValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "options": {}}, "id": "8ac91544-7f82-4254-96a6-ce446b020506", "name": "Accounts", "type": "n8n-nodes-base.switch", "position": [1380, 760], "typeVersion": 3.2}, {"parameters": {"method": "POST", "url": "https://graph.facebook.com/v22.0/*****************/media", "authentication": "predefinedCredentialType", "nodeCredentialType": "facebookGraphApi", "sendQuery": true, "queryParameters": {"parameters": [{"name": "video_url", "value": "={{ $('Baserow').first().json['Production Video URL'] }}"}, {"name": "caption", "value": "={{ $('Baserow').first().json.description }}"}, {"name": "cover_url", "value": "={{ $('Baserow').first().json['Production Video URL'] }}"}, {"name": "media_type", "value": "REELS"}]}, "options": {}}, "id": "34f47c4d-7152-490d-b832-3bb928e7e867", "name": "Instagram Image1", "type": "n8n-nodes-base.httpRequest", "position": [1340, 20], "typeVersion": 4.2, "credentials": {"facebookGraphApi": {"id": "NHcXtjqhFrCLfRlF", "name": "Facebook Graph Bhakti Payanam"}}, "onError": "continueRegularOutput"}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "*****************", "edge": "media_publish", "options": {"queryParameters": {"parameter": [{"name": "creation_id", "value": "={{ $json.id }}"}]}}}, "id": "0ece5fa6-e0c3-46e4-8121-5855f8cc8ec3", "name": "Instragram Post1", "type": "n8n-nodes-base.facebookGraphApi", "position": [1780, 20], "typeVersion": 1, "alwaysOutputData": true, "credentials": {"facebookGraphApi": {"id": "NHcXtjqhFrCLfRlF", "name": "Facebook Graph Bhakti Payanam"}}, "onError": "continueRegularOutput"}, {"parameters": {"amount": 2, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1560, 20], "id": "1d39ec62-1dd3-42db-a0dc-6bd6874769c8", "name": "Wait", "webhookId": "4eca9503-1b69-432b-bb15-ea5e6a018a03"}], "connections": {"Facebook Post": {"main": [[{"node": "Facebook Response", "type": "main", "index": 0}]]}, "Social Media Publishing Router": {"main": [[{"node": "Instagram Image1", "type": "main", "index": 0}], [{"node": "Facebook Post", "type": "main", "index": 0}], [{"node": "Accounts", "type": "main", "index": 0}]]}, "Baserow1": {"main": [[{"node": "Instagram Response", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Baserow1", "type": "main", "index": 0}], []]}, "HTTP Request": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "youtube status", "type": "main", "index": 0}]]}, "youtube status": {"main": [[]]}, "Webhook": {"main": [[{"node": "Baserow", "type": "main", "index": 0}]]}, "Baserow": {"main": [[{"node": "Split Out2", "type": "main", "index": 0}]]}, "Split Out2": {"main": [[{"node": "Social Media Publishing Router", "type": "main", "index": 0}]]}, "@BhaktiPayanam": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "HTTP Request3", "type": "main", "index": 0}]]}, "HTTP Request3": {"main": [[{"node": "youtube status1", "type": "main", "index": 0}]]}, "@GodTrailIndia": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "HTTP Request4": {"main": [[{"node": "Code2", "type": "main", "index": 0}]]}, "Code2": {"main": [[{"node": "HTTP Request5", "type": "main", "index": 0}]]}, "HTTP Request5": {"main": [[{"node": "youtube status2", "type": "main", "index": 0}]]}, "@BhagwantKatha": {"main": [[{"node": "HTTP Request4", "type": "main", "index": 0}]]}, "Accounts": {"main": [[{"node": "@BhaktiPayanam", "type": "main", "index": 0}], [{"node": "@GodTrailIndia", "type": "main", "index": 0}], [{"node": "@BhagwantKatha", "type": "main", "index": 0}]]}, "Instagram Image1": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Instragram Post1": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Instragram Post1", "type": "main", "index": 0}]]}}, "pinData": {"Webhook": [{"headers": {"host": "n8n.syncu.in", "x-real-ip": "**************", "x-forwarded-for": "**************", "x-forwarded-proto": "https", "connection": "upgrade", "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "upgrade-insecure-requests": "1", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "sec-fetch-site": "none", "sec-fetch-mode": "navigate", "sec-fetch-user": "?1", "sec-fetch-dest": "document", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "en-US,en;q=0.9,en-IN;q=0.8,ta;q=0.7", "cookie": "_fbp=fb.1.1722077294689.899089694493538398; _ga=GA1.1.415882120.1722077295; _ga_0M6Z2TZK0T=GS1.1.1736412948.7.0.1736412948.0.0.0; rl_page_init_referrer=RudderEncrypt%3AU2FsdGVkX19IthTtl1D%2FZqsBQYTSEg6Kh7zNH1Q550I%3D; rl_page_init_referring_domain=RudderEncrypt%3AU2FsdGVkX19uxofmOqG7O8qK1d0tqeRPw6fdzphYIkc%3D; user_session=eyJ1c2VyX2lkIjoiMSIsInRva2VuX2hhc2giOiIxNmU4MjBlOWVkMzQzNTIwMzRmMzljNThlMWJkYzMxNzAwMWU2MjcwOGQ4YjgxYzI4ZThiZTJiYjk5ZjI2YmJhIn0%3A1uBb31%3A9O05XPTag3Zt_aa3r44mt1osoGd6AeM1ltsRgrXxKs8; n8n-auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjNmMDMwNTAwLTAyYWQtNGRkNS1iZmMyLTRlYWVhZDdmMzU1OSIsImhhc2giOiJpVmNYaWZRc0JXIiwiYnJvd3NlcklkIjoibzU1cUZSU2lyMmVHdzVmQ0RmWisxZ1dSdjAyei9tKzVyckNnWnJiYlRnUT0iLCJpYXQiOjE3NDY2MzQ4MDksImV4cCI6MTc0NzIzOTYwOX0.CEfChE0yVFxCQUL7E5AVSyuU8YLX2CtOyhCQhpUs0VI; rl_anonymous_id=RudderEncrypt%3AU2FsdGVkX19McetMjwCjDPlOrNwuzHfUjR1L4qf3ofWu8V3BGpOEHoYGAZ66%2FsrbwQDWYTo9Nw7RN90n8n2Q3Q%3D%3D; rl_user_id=RudderEncrypt%3AU2FsdGVkX182AUlybmpgbrxdubPX4ONjPBmnX%2Bs7vQFPFNmhSDR8nv2%2FwWNHQA5akDSaPISu0AF9sPeZnC93FAMtTtLz3IJs4H0C7P1sbHDhp%2Bel2d0aolJp4kf5UMErkhTCW0G9B8871aoxwbrA6kTmAOVIM2CnY%2BL3Cn%2FYL0o%3D; rl_trait=RudderEncrypt%3AU2FsdGVkX19v7sIGthhForH%2FoNIaBlJuxo3lLlcRXIWcsFBbwGBx6XcPaPb7JwBif1jM%2FZOAEXJfpqVMxiuhiekRbx2PY4Z9vPVyJ6TfOdK1IQjnM222mgJ9pQZasIBHlyXaasnl%2Bug%2BQm0sdp%2F%2F2Q%3D%3D; ph_phc_4URIAm1uYfJO7j8kWSe0J8lc8IqnstRLS7Jx8NcakHo_posthog=%7B%22distinct_id%22%3A%2291cc62459c58f7ed708f98af53062f8e1c654113a1dd9f4ac475711fab0cc5c5%233f030500-02ad-4dd5-bfc2-4eaead7f3559%22%2C%22%24sesid%22%3A%5B1746697779876%2C%220196af4a-ce36-7266-a5a7-20027d3ca83c%22%2C1746697637430%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Fn8n.syncu.in%2Fsignin%3Fredirect%3D%25252F%22%7D%7D; rl_session=RudderEncrypt%3AU2FsdGVkX1%2BtVPUV1Er4p0u%2FLMUaDnVc%2FywD0txXPCSa6prpDEeN%2F%2Fb9XiclaXiKdbJPJRRxksn5vC9tHVt7SPv7UNDqJn0aOn9X3cNukM8iVgwcDaHhDw6pFvM6csSQsL%2FUeAw7ajR9TYuTzwTDfA%3D%3D"}, "params": {"RowID": "146"}, "query": {}, "body": {}, "webhookUrl": "https://n8n.syncu.in/webhook-test/9503b9f0-0a5d-4f76-bcbb-78342bf88ac7/post/:RowID", "executionMode": "test"}]}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "91cc62459c58f7ed708f98af53062f8e1c654113a1dd9f4ac475711fab0cc5c5"}}